[2025-06-20 17:49:36,776] [INFO] [pytest_result_log] - Start: hui_testcases/test_save_userInfo.py::Test_Save_userInfo::test_save_userInfo
[2025-06-20 17:49:36,954] [INFO] [apitest] - 【测试环境】开始设置测试环境
[2025-06-20 17:49:36,955] [INFO] [apitest] - 【环境选择】使用环境: ali
[2025-06-20 17:49:36,956] [INFO] [apitest] - 【登录流程】开始登录到 ali 环境
[2025-06-20 17:49:36,957] [INFO] [root] - 【快捷登录】使用快捷函数登录到 ali 环境
[2025-06-20 17:49:36,957] [INFO] [root] - 【登录操作】尝试登录到 ali 环境
[2025-06-20 17:49:36,958] [INFO] [root] - 【环境配置】开始加载 ali 环境配置
[2025-06-20 17:49:36,958] [DEBUG] [root] - 配置文件路径: C:\Users\<USER>\Downloads\API_Online-master-26 - 副本\hui_back\hui_config\hui_online_common.yml
[2025-06-20 17:49:36,958] [DEBUG] [root] - 读取YAML文件: C:\Users\<USER>\Downloads\API_Online-master-26 - 副本\hui_back\hui_config\hui_online_common.yml
[2025-06-20 17:49:36,968] [INFO] [root] - YAML配置中的环境节点: ali
[2025-06-20 17:49:36,969] [INFO] [root] - 成功加载 ali 环境配置
[2025-06-20 17:49:36,969] [INFO] [root] - 【环境节点】ali 环境信息:
[2025-06-20 17:49:36,970] [INFO] [root] -   - URL: http://api-c.sobot.com
[2025-06-20 17:49:36,970] [INFO] [root] -   - 路径: /basic-login/account/consoleLogin/4
[2025-06-20 17:49:36,971] [INFO] [root] -   - 登录用户: <EMAIL>
[2025-06-20 17:49:36,971] [DEBUG] [hui_back.hui_common.v6_console_login] - Current headers before update: {}
[2025-06-20 17:49:36,972] [INFO] [root] - 设置 content-type: application/json (域名: http://api-c.sobot.com)
[2025-06-20 17:49:36,972] [DEBUG] [hui_back.hui_common.v6_console_login] - Headers after update: {'content-type': 'application/json'}
[2025-06-20 17:49:36,972] [DEBUG] [root] - 登录参数: user=<EMAIL>, flag=1, terminalCode=2
[2025-06-20 17:49:36,972] [DEBUG] [root] - 额外参数: {}
[2025-06-20 17:49:36,972] [DEBUG] [root] - 请求数据格式: JSON
[2025-06-20 17:49:36,973] [INFO] [root] - 【API请求】发送登录请求到: http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-20 17:49:36,973] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################
[2025-06-20 17:49:36,974] [INFO] [hui_back.hui_common.v6_console_login] - 请求方法：post
[2025-06-20 17:49:36,974] [INFO] [hui_back.hui_common.v6_console_login] - 请求地址：http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-20 17:49:36,975] [INFO] [hui_back.hui_common.v6_console_login] - 请求参数 json：{'loginUser': '<EMAIL>', 'loginPwd': 'bGl1eWgxMjM=', 'loginFlag': 1, 'terminalCode': 2}
[2025-06-20 17:49:37,645] [INFO] [hui_back.hui_common.v6_console_login] - 响应码：200
[2025-06-20 17:49:37,645] [INFO] [hui_back.hui_common.v6_console_login] - 响应体：{"companyId":"bdbe5660b2eb45758739527fda974091","item":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************.6gMZQu_oGqUNsjzgPspH4FnY65ZzBDHphwgkfsJ-Nek","items":[],"loginCategory":"console,serviceConsole,appConsole","newConsoleFlag":1,"retCode":"000000","retMsg":"操作成功","zone":1}
[2025-06-20 17:49:37,646] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################


[2025-06-20 17:49:37,647] [INFO] [root] - 【API响应】请求完成，耗时: 0.67秒，状态码: 200
[2025-06-20 17:49:37,648] [INFO] [root] - 【响应验证】验证成功，返回码: 000000
[2025-06-20 17:49:37,648] [DEBUG] [root] - 【响应JSON】: {"companyId": "bdbe5660b2eb45758739527fda974091", "item": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJj...
[2025-06-20 17:49:37,648] [DEBUG] [root] - 在 item 字符串中找到会话信息
[2025-06-20 17:49:37,649] [INFO] [root] - 【会话信息】成功提取会话信息: eyJ0eXAiOiJKV1Q...
[2025-06-20 17:49:37,649] [INFO] [root] - 【登录结果】登录成功，获取到会话信息
[2025-06-20 17:49:37,650] [INFO] [apitest] - 【登录成功】环境: ali, 会话ID: eyJ0eXAiOiJKV1Q...
[2025-06-20 17:49:37,653] [INFO] [apitest] - 【测试准备】环境设置完成，测试前登录成功
[2025-06-20 17:49:38,591] [ERROR] [pytest_result_log] - test status is FAILED (hui_testcases/test_save_userInfo.py::Test_Save_userInfo::test_save_userInfo): in __getattr__
[2025-06-20 17:49:38,595] [DEBUG] [pytest_result_log] - hui_testcases/test_save_userInfo.py::Test_Save_userInfo::test_save_userInfo -> hui_testcases\test_save_userInfo.py:26: in test_save_userInfo
    init_resp = user_init.send_request()
hui_api\user_init.py:39: in send_request
    'partnerId': fake.number(),
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\faker\proxy.py:130: in __getattr__
    return getattr(self._factories[0], attr)
E   AttributeError: 'Generator' object has no attribute 'number'
[2025-06-20 17:49:38,596] [INFO] [pytest_result_log] - End: hui_testcases/test_save_userInfo.py::Test_Save_userInfo::test_save_userInfo
[2025-06-20 17:49:38,598] [INFO] [apitest] - 【测试完成】测试会话结束，清理环境
