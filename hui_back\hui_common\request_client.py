import json
import urllib

import jsonpath
import requests

from .file_load import load_yaml_file
from .logger import GetLogger
from .variables_manager import VariablesManager
from .request_processor import RequestProcessor


class ResponseWrapper:
    """
    响应包装器：同时提供响应对象和RequestsClient对象的功能
    支持链式调用和向后兼容
    """
    def __init__(self, request_client):
        self.request_client = request_client
        self.response = request_client.resp
        # 移除自动提取cid的逻辑，只在显式调用extract_cid或extract_and_save时提取
    
    # 创建logger属性
    @property
    def logger(self):
        return self.request_client.logger
    
    # 代理响应对象的属性
    @property
    def status_code(self):
        return self.response.status_code
    
    @property
    def text(self):
        return self.response.text
    
    @property
    def headers(self):
        return self.response.headers
    
    @property
    def cookies(self):
        return self.response.cookies
    
    @property
    def url(self):
        return self.response.url
    
    # 代理常用响应方法
    def json(self):
        return self.response.json()
    
    def content(self):
        return self.response.content
    
    # 代理RequestsClient的方法，支持链式调用
    def extract_resp(self, *args, **kwargs):
        return self.request_client.extract_resp(*args, **kwargs)
    
    def extract_and_save(self, *args, **kwargs):
        return self.request_client.extract_and_save(*args, **kwargs)
    
    def extract_cid(self, *args, **kwargs):
        return self.request_client.extract_cid(*args, **kwargs)
    
    def send_and_extract(self, *args, **kwargs):
        return self.request_client.send_and_extract(*args, **kwargs)
    
    # status_code作为方法的情况下，直接使用属性
    def get_status_code(self):
        return self.status_code
    
    # 魔术方法，使其可以作为RequestsClient对象使用
    def __getattr__(self, name):
        # 处理status_code方法冲突
        if name == 'status_code' and callable(getattr(self.request_client, 'status_code', None)):
            return self.get_status_code
        
        # 如果属性不在ResponseWrapper中定义，则尝试从response或request_client获取
        if hasattr(self.response, name):
            return getattr(self.response, name)
        elif hasattr(self.request_client, name):
            return getattr(self.request_client, name)
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
    
    # 让它可以被当作响应对象调用
    def __call__(self, *args, **kwargs):
        # 如果response是可调用的，则调用它
        if callable(self.response):
            return self.response(*args, **kwargs)
        # 否则返回self，支持链式调用
        return self

    def extract_value(self, *args, **kwargs):
        return self.request_client.extract_value(*args, **kwargs)


class RequestsClient:

    def __init__(self):
        # 最初，host的负值是放到这里的，以self.host = commonConfig['openapiHost']的形式实现的
        # self.host = None
        self.logger = GetLogger.get_logger()
        self.session = requests.session()
        self.url = None
        self.method = None
        self.headers = None
        self.params = None
        self.data = None
        self.json = None
        self.files = None
        self.resp = None
        # 初始化变量管理器
        self.variables_manager = VariablesManager()
        # self.aes = AesEncrypt(self.commonConfig['aes_private'])
        # self.location = DIR_NAME

    @RequestProcessor.process_request
    def send_request(self, **kwargs):
        if kwargs.get('url') == None:
            kwargs['url'] = self.url
        if kwargs.get('method') == None:
            kwargs['method'] = self.method
        if kwargs.get('headers') == None:
            kwargs['headers'] = self.headers
        if kwargs.get('params') == None:
            kwargs['params'] = self.params
        if kwargs.get('data') == None:
            kwargs['data'] = self.data
        if kwargs.get('json') == None:
            kwargs['json'] = self.json
        if kwargs.get('files') == None:
            kwargs['files'] = self.files
            
        # 处理请求参数中的变量引用（${var}）
        if kwargs.get('params') is not None:
            kwargs['params'] = self.variables_manager.process_dict_values(kwargs['params'])
        if kwargs.get('data') is not None:
            kwargs['data'] = self.variables_manager.process_dict_values(kwargs['data'])
        if kwargs.get('json') is not None:
            kwargs['json'] = self.variables_manager.process_dict_values(kwargs['json'])
        if kwargs.get('url') is not None:
            kwargs['url'] = self.variables_manager.replace_variables(kwargs['url'])
            
        # Handle application/x-www-form-urlencoded content type
        content_type = kwargs.get("headers", {}).get("content-type", "").lower()
        if "application/x-www-form-urlencoded" in content_type:
            if kwargs.get('data') is not None:
                kwargs['data'] = urllib.parse.urlencode(kwargs['data'])  # urlencode需要转换成字符串
                if '%5B%5D' in kwargs['data']:
                    kwargs['data'] = kwargs['data'].replace('%5B%5D', '')  # 去掉NONE 转换成空，不然接口会报错
                if 'None' in kwargs['data']:
                    kwargs['data'] = kwargs['data'].replace('None', '')  # 去掉NONE 转换成空，不然接口会报错
        try:
            url = kwargs.pop('url')
            method = kwargs.pop('method')
            if isinstance(method, str) and url:
                func = getattr(self.session, method.lower())
                self.logger.info('#######################################################################')
                self.logger.info(f'请求方法：{method}')
                self.logger.info(f'请求地址：{url}')
                if kwargs.get('params'):
                    self.logger.info(f'请求参数 params：{kwargs.get("params")}')
                if kwargs.get('data'):
                    self.logger.info(f'请求参数 data：{kwargs.get("data")}')
                if kwargs.get('json'):
                    self.logger.info(f'请求参数 json：{kwargs.get("json")}')
                self.resp = func(url, **kwargs)
                self.logger.info(f'响应码：{self.resp.status_code}')
                self.logger.info(f'响应体：{self.resp.text}')
                self.logger.info('#######################################################################' + "\n\n")
                
                # 同时实现返回self和返回response的功能，保持兼容性
                # 在RequestsClient类上定义所有需要代理的response属性和方法
                return ResponseWrapper(self)
            else:
                self.logger.error('请求方法或URL参数为空')
                self.resp = None
                raise Exception('请求方法或URL参数为空')
        except Exception as e:
            self.logger.error(f'请求发送出错：{e}')
            self.resp = None
            # 重新抛出异常，让调用方知道请求失败了
            raise

    def extract_resp(self, jsonpath_express, index=0, var_name=None):
        # 检查self.resp是否为None
        if self.resp is None:
            self.logger.error('响应对象为None，请确保先调用send_request()方法且请求成功')
            return None
            
        text = self.resp.text

        if text != '':
            try:
                self.logger.info('#######################################################################')
                try:
                    resp_dict = json.loads(text)  # 将json格式的字符串转换成字典
                    # 打印完整的响应内容，方便诊断
                    self.logger.info(f'完整响应内容: {json.dumps(resp_dict, ensure_ascii=False, indent=2)[:1000]}{"..." if len(json.dumps(resp_dict)) > 1000 else ""}')
                except json.JSONDecodeError as json_err:
                    self.logger.error(f'JSON解析错误: {json_err}，响应内容: {text}')
                    return None
                
                if index < 0:
                    res = jsonpath.jsonpath(resp_dict, jsonpath_express)
                    if res:
                        self.logger.info('通过【{}】提取到的结果是:{}'.format(jsonpath_express, res))
                    else:
                        self.logger.warning(f'通过【{jsonpath_express}】未找到匹配结果，请检查JSONPath表达式是否正确或响应中是否包含该字段')
                        # 打印可用的顶级字段，帮助排查
                        if isinstance(resp_dict, dict):
                            self.logger.info(f'响应中可用的顶级字段: {list(resp_dict.keys())}')
                    self.logger.info('#######################################################################' + "\n\n")
                    
                    # 如果提供了变量名，则存储提取的值
                    if var_name is not None:
                        self.variables_manager.set_variable(var_name, res)
                    
                    return res
                else:
                    res = jsonpath.jsonpath(resp_dict, jsonpath_express)
                    if res:
                        result = res[index]
                        self.logger.info('通过【{}】提取到的结果是:{}'.format(jsonpath_express, result))
                        
                        # 如果提供了变量名，则存储提取的值
                        if var_name is not None:
                            self.variables_manager.set_variable(var_name, result)
                            
                        self.logger.info('#######################################################################' + "\n\n")
                        return result
                    else:
                        self.logger.warning(f'通过【{jsonpath_express}】未找到匹配结果，请检查JSONPath表达式是否正确或响应中是否包含该字段')
                        # 打印可用的顶级字段，帮助排查
                        if isinstance(resp_dict, dict):
                            self.logger.info(f'响应中可用的顶级字段: {list(resp_dict.keys())}')
                        self.logger.info('#######################################################################' + "\n\n")
                        return None
            except Exception as e:
                self.logger.debug('从响应中通过【{}】提取是出现了异常:{}'.format(jsonpath_express, e))
                self.logger.info('#######################################################################' + "\n\n")
                return None
        else:
            self.logger.warning('响应内容为空，无法进行提取')
            return None

    def extract_and_save(self, jsonpath_express, var_name=None, index=0):
        """
        从响应中提取数据并保存为变量
        :param jsonpath_express: JSONPath表达式（必填）
        :param var_name: 变量名（选填）, 如果不提供，会自动从jsonpath_express中提取最后一部分作为变量名
        :param index: 索引，默认为0（选填）
        :return: 提取的值，如果所有路径都未找到则返回None
        """
        # 如果未提供变量名，则从jsonpath_express中提取最后一部分
        if var_name is None:
            # 从jsonpath表达式中提取最后一个属性名作为变量名
            # 例如 $.data.items[0].cid 会提取 cid 作为变量名
            import re
            match = re.search(r'\.([^.\[\]]+)(?:\[\d+\])?$', jsonpath_express)
            if match:
                var_name = match.group(1)
            else:
                # 如果无法提取，则使用整个表达式作为变量名，去掉特殊字符
                var_name = re.sub(r'[^\w]', '_', jsonpath_express).strip('_')
            
            self.logger.info(f'未提供变量名，自动生成变量名：{var_name}')
        
        # 首先尝试指定的JSONPath
        value = self.extract_resp(jsonpath_express, index)
        
        # 如果指定路径没有找到值，并且是在尝试提取cid
        if value is None and (var_name == 'cid' or jsonpath_express.endswith('.cid')):
            self.logger.info(f'通过路径 {jsonpath_express} 没有找到cid，尝试使用extract_cid方法搜索更多路径')
            # 尝试使用extract_cid方法，它会自动尝试多种路径
            value = self.extract_cid(var_name)
            if value is not None:
                self.logger.info(f'成功通过extract_cid方法找到值: {value}')
                # extract_cid方法已经保存了变量，不需要再次保存
                return value
        
        # 新增：如果指定路径没有找到值，并且是在尝试提取userId
        if value is None and (var_name == 'userId' or jsonpath_express.endswith('.userId')):
            self.logger.info(f'通过路径 {jsonpath_express} 没有找到userId，尝试使用extract_userId方法搜索更多路径')
            value = self.extract_userId(var_name)
            if value is not None:
                self.logger.info(f'成功通过extract_userId方法找到值: {value}')
                return value
        
        # 如果指定路径找到了值，保存它
        if value is not None:
            self.variables_manager.set_variable(var_name, value)
            self.logger.info(f'将提取的值【{value}】保存为变量【{var_name}】')
        else:
            self.logger.warning(f'无法从路径【{jsonpath_express}】提取到值，变量【{var_name}】未保存')
            
            # 如果是查找cid但是没有找到，尝试查找id字段
            if var_name == 'cid' or jsonpath_express.endswith('.cid'):
                # 获取id的基础路径（去掉.cid部分）
                base_path = jsonpath_express.rsplit('.cid', 1)[0]
                # 尝试.id路径
                id_path = f"{base_path}.id"
                id_value = self.extract_resp(id_path, index)
                if id_value is not None:
                    self.logger.info(f'未找到cid，但在路径【{id_path}】找到了id值: {id_value}')
                    self.variables_manager.set_variable(var_name, id_value)
                    self.logger.info(f'将id值【{id_value}】保存为变量【{var_name}】')
                    return id_value
        
        return value

    def status_code(self):
        return self.resp.status_code

    def decry_data(self):
        text = self.resp.text
        return self.aes.decrypt(text)

    def send_and_extract(self, jsonpath_express, var_name=None, index=0, **kwargs):
        """
        发送请求并提取数据的便捷方法，包含错误处理
        :param jsonpath_express: JSONPath表达式（必填）
        :param var_name: 变量名（选填），如果不提供会自动从jsonpath_express中提取
        :param index: 索引，默认为0（选填）
        :param kwargs: 请求参数，与send_request方法相同
        :return: 提取的值，如果请求失败则返回None
        """
        try:
            resp = self.send_request(**kwargs)
            if resp and resp.status_code == 200:  # 只在请求成功时提取数据
                return self.extract_and_save(jsonpath_express, var_name, index)
            else:
                status_code = getattr(resp, 'status_code', None)
                self.logger.error(f'请求失败或状态码异常: {status_code}')
                return None
        except Exception as e:
            self.logger.error(f'请求或数据提取过程中发生异常: {e}')
            return None

    def extract_cid(self, var_name='cid'):
        """
        从响应中提取cid的辅助方法，尝试多种常见路径
        :param var_name: 存储提取值的变量名，默认为'cid'
        :return: 提取的cid值，如果所有路径都未找到则返回None
        """
        if self.resp is None:
            self.logger.error('响应对象为None，请确保先调用send_request()方法且请求成功')
            return None
            
        text = self.resp.text
        if not text:
            self.logger.warning('响应内容为空，无法提取cid')
            return None
            
        try:
            resp_dict = json.loads(text)
            
            # 常见的cid路径
            cid_paths = [
                '$.cid',           # 顶级cid
                '$.data.cid',      # data对象中的cid
                '$.result.cid',    # result对象中的cid
                '$.item.cid',      # item对象中的cid
                '$.items[0].cid',  # items数组第一项中的cid
                '$.response.cid',  # response对象中的cid
                '$.content.cid',   # content对象中的cid
                '$.body.cid',      # body对象中的cid
                '$.payload.cid',   # payload对象中的cid
                '$.serverData.cid', # serverData对象中的cid
                '$.resultData.cid', # resultData对象中的cid
                '$.info.cid'       # info对象中的cid
            ]
            
            # 尝试直接在响应中查找名为"cid"的字段(深度搜索)
            cid_deep_search = jsonpath.jsonpath(resp_dict, '$..cid')
            
            # 记录诊断信息
            self.logger.info('#######################################################################')
            self.logger.info(f'尝试提取cid，响应内容: {json.dumps(resp_dict, ensure_ascii=False)[:200]}...')
            
            if isinstance(resp_dict, dict):
                self.logger.info(f'响应中顶级字段: {list(resp_dict.keys())}')
            
                # 如果响应有 "status" 和 "msg" 字段，可能是成功响应
                if 'status' in resp_dict and resp_dict.get('status') == 0:
                    self.logger.info('响应显示请求成功，继续尝试提取cid')
            
            # 深度搜索结果
            if cid_deep_search:
                self.logger.info(f'通过深度搜索找到的cid值: {cid_deep_search}')
                cid_value = cid_deep_search[0]  # 使用第一个找到的值
                self.variables_manager.set_variable(var_name, cid_value)
                self.logger.info(f'已将cid值 {cid_value} 保存为变量 ${var_name}')
                self.logger.info('#######################################################################' + "\n\n")
                return cid_value
                
            # 尝试各种路径
            for path in cid_paths:
                cid_value = jsonpath.jsonpath(resp_dict, path)
                if cid_value:
                    self.logger.info(f'通过路径 {path} 找到cid值: {cid_value[0]}')
                    cid_value = cid_value[0]
                    self.variables_manager.set_variable(var_name, cid_value)
                    self.logger.info(f'已将cid值 {cid_value} 保存为变量 ${var_name}')
                    self.logger.info('#######################################################################' + "\n\n")
                    return cid_value
            
            # 如果是简单响应，尝试直接返回整个响应作为cid
            if isinstance(resp_dict, str) and resp_dict.strip():
                self.logger.info(f'响应是字符串，直接作为cid: {resp_dict}')
                self.variables_manager.set_variable(var_name, resp_dict)
                self.logger.info(f'已将值 {resp_dict} 保存为变量 ${var_name}')
                self.logger.info('#######################################################################' + "\n\n")
                return resp_dict
            
            # 查找任何可能包含"cid"的字段
            for key in resp_dict.keys() if isinstance(resp_dict, dict) else []:
                if 'cid' in key.lower():
                    self.logger.info(f'找到可能的cid字段: {key}，值: {resp_dict[key]}')
                    self.variables_manager.set_variable(var_name, resp_dict[key])
                    self.logger.info(f'已将值 {resp_dict[key]} 保存为变量 ${var_name}')
                    self.logger.info('#######################################################################' + "\n\n")
                    return resp_dict[key]
            
            # 尝试提取任何可能的ID字段
            id_search = jsonpath.jsonpath(resp_dict, '$..id')
            if id_search:
                self.logger.info(f'未找到cid，但找到了id: {id_search[0]}')
                self.variables_manager.set_variable(var_name, id_search[0])
                self.logger.info(f'已将id值 {id_search[0]} 保存为变量 ${var_name}')
                self.logger.info('#######################################################################' + "\n\n")
                return id_search[0]
            
            # 如果响应中有 "visitId" 字段，可能是用户会话ID
            visit_id = jsonpath.jsonpath(resp_dict, '$..visitId')
            if visit_id:
                self.logger.info(f'未找到cid，但找到了visitId: {visit_id[0]}')
                self.variables_manager.set_variable(var_name, visit_id[0])
                self.logger.info(f'已将visitId值 {visit_id[0]} 保存为变量 ${var_name}')
                self.logger.info('#######################################################################' + "\n\n")
                return visit_id[0]
            
            # 如果所有尝试都失败
            self.logger.warning('无法从响应中提取cid，请检查响应格式或手动指定正确的JSONPath')
            self.logger.info('#######################################################################' + "\n\n")
            return None
            
        except Exception as e:
            self.logger.error(f'提取cid时发生异常: {e}')
            self.logger.info('#######################################################################' + "\n\n")
            return None

    def extract_userId(self, var_name='userId'):
        """
        从响应中提取userId的辅助方法，尝试多种常见路径
        :param var_name: 存储提取值的变量名，默认为'userId'
        :return: 提取的userId值，如果所有路径都未找到则返回None
        """
        if self.resp is None:
            self.logger.error('响应对象为None，请确保先调用send_request()方法且请求成功')
            return None
        
        text = self.resp.text
        if not text:
            self.logger.warning('响应内容为空，无法提取userId')
            return None
        
        try:
            resp_dict = json.loads(text)
            # 常见的userId路径
            userId_paths = [
                '$.userId',           # 顶级userId
                '$.data.userId',      # data对象中的userId
                '$.result.userId',    # result对象中的userId
                '$.item.userId',      # item对象中的userId
                '$.items[0].userId',  # items数组第一项中的userId
                '$.response.userId',  # response对象中的userId
                '$.content.userId',   # content对象中的userId
                '$.body.userId',      # body对象中的userId
                '$.payload.userId',   # payload对象中的userId
                '$.serverData.userId', # serverData对象中的userId
                '$.resultData.userId', # resultData对象中的userId
                '$.info.userId'       # info对象中的userId
            ]
            # 尝试直接在响应中查找名为"userId"的字段(深度搜索)
            userId_deep_search = jsonpath.jsonpath(resp_dict, '$..userId')
            self.logger.info('#######################################################################')
            self.logger.info(f'尝试提取userId，响应内容: {json.dumps(resp_dict, ensure_ascii=False)[:200]}...')
            if isinstance(resp_dict, dict):
                self.logger.info(f'响应中顶级字段: {list(resp_dict.keys())}')
            # 深度搜索结果
            if userId_deep_search:
                # 过滤掉 None、空字符串等无效值
                valid_userId = next((v for v in userId_deep_search if v not in (None, '', [], {})), None)
                if valid_userId is not None:
                    self.logger.info(f'通过深度搜索找到的userId值: {valid_userId}')
                    self.variables_manager.set_variable(var_name, valid_userId)
                    self.logger.info(f'已将userId值 {valid_userId} 保存为变量 ${var_name}')
                    self.logger.info('#######################################################################' + "\n\n")
                    return valid_userId
            # 尝试各种路径
            for path in userId_paths:
                userId_value = jsonpath.jsonpath(resp_dict, path)
                # 过滤掉 None、空字符串、空列表、空字典等无效值
                valid_userId = next((v for v in userId_value if v not in (None, '', [], {})), None) if userId_value else None
                if valid_userId is not None:
                    self.logger.info(f'通过路径 {path} 找到userId值: {valid_userId}')
                    self.variables_manager.set_variable(var_name, valid_userId)
                    self.logger.info(f'已将userId值 {valid_userId} 保存为变量 ${var_name}')
                    self.logger.info('#######################################################################' + "\n\n")
                    return valid_userId
            # 如果所有尝试都失败
            self.logger.warning('无法从响应中提取userId，请检查响应格式或手动指定正确的JSONPath')
            self.logger.info('#######################################################################' + "\n\n")
            return None
        except Exception as e:
            self.logger.error(f'提取userId时发生异常: {e}')
            self.logger.info('#######################################################################' + "\n\n")
            return None

    def extract_value(self, jsonpath_express, index=0, var_name=None):
        """
        从响应中提取值的辅助方法，同extract_resp
        提供更直观的方法名称
        :param jsonpath_express: JSONPath表达式
        :param index: 索引，默认为0
        :param var_name: 变量名，可选
        :return: 提取的值，如果没有找到则返回None
        """
        return self.extract_resp(jsonpath_express, index, var_name)

    @property
    def response(self):
        """
        获取原始响应对象
        :return: 响应对象
        """
        return self.resp

    def get_resp(self):
        """
        获取原始响应对象的兼容性方法
        :return: 响应对象
        """
        return self.resp
