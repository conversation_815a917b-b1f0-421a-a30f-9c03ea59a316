ali:
  # 默认基础配置
  default_url: https://api-c.soboten.com  # 默认基础URL，大部分接口使用

  # 登录接口配置（使用特殊域名和协议）
  url: http://api-c.sobot.com  # 登录接口专用域名
  path: /basic-login/account/consoleLogin/4
  loginUser: <EMAIL>
  loginPwd: bGl1eWgxMjM=
  loginFlag: 1
  terminalCode: 02

  # 用户初始化接口配置（使用特殊协议）
  user_url: http://api-c.soboten.com  # 用户初始化使用http协议
  user_init_path: /text/chat-visit/user/init/v6
  sysNum: bdbe5660b2eb45758739527fda974091
  X: 56de2146104f480d8418d5400c978305

  # 在线客服工作台-在线状态（使用特殊域名）
  customer_online_status_url: https://api-c.sobot.com  # 在线状态使用sobot域名
  customer_online_status_path: /text/chat-kwb/admin/online.action
  Y: xOh8P5f7pLDPHW5kyWJscuzHfKg8v3Wj5TWF2jP6WSj+IBS1zqXJr8fZ4ny5vE8t

  # 以下接口使用默认URL（https://api-c.soboten.com），只需配置path
  # 用户转人工接口
  trans_human_path: /text/chat-web/user/chatconnect.action

  # 无效服务总结接口
  services_summary_path: /text/chat-kwb/conversation/summarySubmitVer2.action

  # 在线工作台-标星接口
  stars_path: /text/chat-kwb/admin/add_marklist.action

  # 客服发送消息
  customer_sendMsg_path: /text/chat-kwb/message/send.action

  # 用户发送消息
  user_sendMsg_path: /text/chat-web/message/user/send.action

  # 保存用户信息
  save_userInfo_path: /text/chat-kwb/admin/addCustomerInfo.action


# ten:
#   # 登录接口配置
#   url: http://api-c.soboten.com
#   path: /text/basic-login/account/consoleLogin/4
#   loginUser: <EMAIL>
#   loginPwd: QGxpdXloMTIz
#   loginFlag: 1
#   terminalCode: 02
#   # 用户初始化接口配置
#   user_url: http://api-c.soboten.com
#   user_init_path: /text/chat-visit/user/init/v6
#   sysNum: e2f181dfa5214ad0aab9909b7e262e31
#   #在线客服工作台-在线状态-在线
#   customer_online_status_path: /text/chat-kwb/admin/online.action
#   customer_online_status_url: https://api-c.soboten.com 
#   uid: H1hz6lTjZ2u7SGkPrC88N67X0o6hPSKwPvoa4Nb30rL+IBS1zqXJr56i2Z2LvOnJ


# sg:
#   # 登录接口配置
#   url: http://sg.sobot.com
#   path: /basic-login/serviceLoginNew/4
#   loginUser: <EMAIL>
#   loginPwd: bGl1eWgxMjNA
#   loginFlag: 1
#   terminalCode: 02
#   # 用户初始化接口配置
#   user_url: http://sg.sobot.com
#   user_init_path: /chat-visit/user/init/v6
#   sysNum: 56d94fd7e7764affaa02c05f6b1687ae
#   #在线客服工作台-在线状态-在线
#   customer_online_status_path: /chat-kwb/admin/online.action
#   customer_online_status_url: https://sg.sobot.io
#   uid: 2GU2b39L4wGvMiN0mkc5wuY6a3ndKGIPkdOivVwYjKz+IBS1zqXJr4/Wcu4ssbjx
  



# us:
#   # 登录接口配置
#   url: http://us.sobot.com
#   path: /basic-login/serviceLoginNew/4
#   loginUser: <EMAIL>
#   loginPwd: bGl1eWgxMjNA
#   loginFlag: 1
#   terminalCode: 02
#   # 用户初始化接口配置
#   user_url: http://us.sobot.com
#   user_init_path: /chat-visit/user/init/v6
#   sysNum: d0c58400e34040f3868bfa9051750a93
#   #在线客服工作台-在线状态-在线
#   customer_online_status_path: /chat-kwb/admin/online.action
#   customer_online_status_url: https://us.sobot.io
#   uid: 8RJNZdCzNJIv+DFr7NTzF32l/JDITJ7JE+3bMQW019r+IBS1zqXJr5SDH+q9vmTQ

  



# hk:
#   # 登录接口配置
#   url: http://hk.sobot.com
#   path: /basic-login/serviceLoginNew/4
#   loginUser: <EMAIL>
#   loginPwd: bGl1eWgxMjM=
#   loginFlag: 1
#   terminalCode: 02
#   # 用户初始化接口配置
#   user_url: http://hk.sobot.com
#   user_init_path: /chat-visit/user/init/v6
#   sysNum: 30bf29c66db14eb58bae0b489bd27f3c
#   #在线客服工作台-在线状态-在线
#   customer_online_status_path: /chat-kwb/admin/online.action
#   customer_online_status_url: http://hk.sobot.com
#   uid: /Q8ZkIDUx+HvhV8UYTNr2igzJS3QWNGZmx9XgfyAuG5SPCQEDe0Q85IVJSLrSzmZ


# sg-grey:
#   # 登录接口配置
#   url: http://sg-grey.sobot.io
#   path: /basic-login/serviceLoginNew/4
#   loginUser: <EMAIL>
#   loginPwd: bGl1eWgxMjM=
#   loginFlag: 1
#   terminalCode: 02
#   # 用户初始化接口配置
#   user_url: http://sg-grey.sobot.io
#   user_init_path: /chat-visit/user/init/v6
#   sysNum: 1e320e346a4e40ffb384ae9c9f165e76
#   #在线客服工作台-在线状态-在线
#   customer_online_status_path: /chat-kwb/admin/online.action
#   customer_online_status_url: http://sg-grey.sobot.io
#   uid: a57LhklSUe2fXAHXftsfK6UPIJX+EsVPzOLUQ7EDbItSPCQEDe0Q87xpbqaoJFLv



  
  
  
  


  
