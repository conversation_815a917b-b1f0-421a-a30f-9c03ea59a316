"""
用户初始化接口
"""
from ..hui_common.base_api import OnlineBaseSaasApi
from faker import Faker
fake = Faker(locale='zh_CN') # 生成中文文本

class UserInit(OnlineBaseSaasApi):
    """用户初始化API类 """

    def send_request(self, **kwargs):
        """通用方法实现：配置 + 刷新变量 + 发送请求"""
        # 获取配置并一次性完成所有设置
        config = self.configInfo

        # 配置API - 使用新的URL构建逻辑
        self.url = self.build_url('user_url', 'user_init_path')
        self.method, self.headers, self.json = 'post', {'content-type': 'application/x-www-form-urlencoded'}, None
        self.data = {
            'source': 0,
            'lanFlag': '',
            'locale': '',
            'language': 'zh',
            'robotFlag': '',
            'channelFlag': '',
            'platformUnionCode': '',
            'faqId': '',
            'schemeId': '',
            'ruleId': '',
            'ack': 1,
            'isReComment': 1,
            'chooseAdminId': '',
            'agid': config.get('X', ''),
            'aid': '',
            'uid': config.get('X', ''),  # 使用配置中的X值作为初始uid
            'tranFlag': '',
            'groupId': '',
            'partnerId': fake.random_int(min=1000, max=9999),
            # 'partnerId': '',
            'tel': '',
            'email': '',
            'visitUrl': '',
            'face': '',
            'weibo': '',
            'weixin': '',
            'qq': '',
            'sex': '',
            'birthday': '',
            'remark': '',
            'params': '',
            'customerFields': '',
            'visitStartTime': '',
            'multiParams': '',
            'summaryParams': '',
            'sign': '',
            'newFlag': 1,
            'flowType': '',
            'flowCompanyId': '',
            'flowGroupId': '',
            'isVip': '',
            'vipLevel': '',
            'userLabel': '',
            'xst': config.get('X', ''),
            'toTiao_clickId': '',
            'sogou_logidUrl': '',
            'isJs': 0,
            'joinType': '',
            'shopifyDomain': '',
            'shopifyShopId': '',
            'countryName': '',
            'visitTitle': '',
            'realname': '',
            'enterpriseName': '',
            'sysNum': config.get('sysNum', ''),
            'uame': '',
            'timezoneId': '',
            'cid': config.get('X', '')  # 添加cid字段
        }

        # 发送请求并获取响应
        response = super().send_request(**kwargs)

        # 提取并保存响应中的关键字段
        if response:
            # 提取响应字段
            self.cid = response.extract_and_save('$.cid', 'cid')
            self.uid = response.extract_and_save('$.uid', 'uid')
            self.puid = response.extract_and_save('$.puid', 'puid')
            self.userId = response.extract_and_save('$.userId', 'userId')

            # 提取响应中的其他有用字段
            self.companyId = response.extract_and_save('$.companyId', 'companyId')
            self.schemeId = response.extract_and_save('$.schemeId', 'schemeId')
            self.visitSchemeId = response.extract_and_save('$.visitSchemeId', 'visitSchemeId')

            # 使用通用方法提取请求参数（用于其他接口复用）
            request_params = self.extract_and_save_params([
                'partnerId', 'sysNum', 'agid', 'xst', 'source', 'language',
                'ack', 'isReComment', 'newFlag', 'isJs'
            ])

            # 设置实例属性以便直接访问
            self.partnerId = request_params.get('partnerId')
            self.sysNum = request_params.get('sysNum')
            self.agid = request_params.get('agid')
            self.xst = request_params.get('xst')

            print(f"用户初始化完成，提取的响应字段:")
            print(f"  uid: {self.uid}")
            print(f"  cid: {self.cid}")
            print(f"  puid: {self.puid}")
            print(f"  userId: {self.userId}")
            print(f"  companyId: {self.companyId}")
            print(f"  schemeId: {self.schemeId}")

            print(f"用户初始化完成，提取的请求参数:")
            print(f"  partnerId: {self.partnerId}")
            print(f"  sysNum: {self.sysNum}")
            print(f"  agid: {self.agid}")
            print(f"  xst: {self.xst}")

            # 显示所有提取的请求参数
            if request_params:
                print(f"所有提取的请求参数: {list(request_params.keys())}")

        return response
