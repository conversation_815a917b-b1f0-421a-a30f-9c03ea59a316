"""
用户初始化接口
"""
from ..hui_common.base_api import OnlineBaseSaasApi

class UserInit(OnlineBaseSaasApi):
    """用户初始化API类 """

    def send_request(self, **kwargs):
        """通用方法实现：配置 + 刷新变量 + 发送请求"""
        # 获取配置并一次性完成所有设置
        config = self.configInfo

        # 配置API - 使用新的URL构建逻辑
        self.url = self.build_url('user_url', 'user_init_path')
        self.method, self.headers, self.json = 'post', {'content-type': 'application/x-www-form-urlencoded'}, None

        # 使用抽离的方法设置请求数据
        self.data = self.setup_user_init_data(config)

        # 发送请求并获取响应
        response = super().send_request(**kwargs)

        # 提取参数
        if response:
            extraction_result = self.extract_user_init_params(response)
            self.print_extracted_params(extraction_result)

        return response
