"""
保存用户信息
"""
from ..hui_common.base_api import OnlineBaseSaasApi
from ..hui_common.time_utils import get_current_timestamp
from faker import Faker
fake = Faker(locale='zh_CN') # 生成中文文本



class Save_userInfo(OnlineBaseSaasApi):
    """保存用户信息API类"""

    def send_request(self, **kwargs):
        """通用方法实现：配置 + 刷新变量 + 发送请求"""
        # 获取配置并一次性完成所有设置
        config = self.configInfo


        # 配置API - 使用新的URL构建逻辑
        self.url = self.build_url('save_userInfo_url', 'save_userInfo_path')
        self.method, self.headers, self.json = 'post', {'content-type': 'application/x-www-form-urlencoded'}, None


        self.data = {
            'uid': '${uid}',
            'customerFields': '',
            'sender': config.get('Y', ''),
            'content': fake.sentence(),
            'userId': '${userId}',
            'tel': "['14589888619', '13107540315', '15341290420', '13915229439', '13591304092']",
            'email': '<EMAIL>',
            'uname': fake.name(), 
            'realname': fake.name(),
            'qq': '19000000000',
            'remark': '备注',
            'face': '',
            'weixin': '',
            'weibo': '',
            'sex': '',
            'age': '',
            'birthday': '',
            'partnerId': '${partnerId}',  # 使用从用户初始化中提取的partnerId
            'createSource': '',
            'tid': config.get('Y', ''),
            'proviceId': '',
            'proviceName': '',
            'cityId': '',
            'cityName': fake.city(),
            'areaId': '',
            'areaName': '',
            'enterpriseId': '',
            'enterpriseName': '',
            'countryId': '',
            'countryName': '',
            'isVip': '',
            'vipLevel': '',
            'userLabel': ''



        }

        # 刷新变量并发送请求（包含从用户初始化提取的参数）
        self.refresh_data_with_extracted_params('uid', 'userId', 'partnerId', 'cid')

        # # 获取真实uid并生成msgId
        # uid = self.data.get('uid', '')
        # msg_id = f"{uid}{get_current_timestamp()}"
        # self.data['msgId'] = msg_id

        # 发送请求并处理会话断开错误
        try:
            response = super().send_request(**kwargs)

            # 检查响应是否包含会话断开错误
            if response and hasattr(response, 'resp') and response.resp:
                try:
                    import json
                    resp_data = json.loads(response.resp.text)
                    if resp_data.get('retCode') == '210029':
                        self.logger.warning("检测到会话断开错误(210029)，这通常是因为：")
                        self.logger.warning("1. 用户初始化失败，会话未建立")
                        self.logger.warning("2. 会话已超时或被服务器清理")
                        self.logger.warning("3. 请求参数中的会话标识无效")
                        self.logger.warning("建议：确保用户初始化成功后再调用此接口")

                        # 检查关键会话参数
                        uid = self.variables_manager.get_variable('uid')
                        userId = self.variables_manager.get_variable('userId')
                        cid = self.variables_manager.get_variable('cid')

                        self.logger.info(f"当前会话参数: uid={uid}, userId={userId}, cid={cid}")

                        if not uid or not userId:
                            self.logger.error("关键会话参数缺失，这是导致210029错误的主要原因")

                except (json.JSONDecodeError, AttributeError):
                    pass  # 如果无法解析响应，继续正常流程

            return response

        except Exception as e:
            self.logger.error(f"发送请求时发生异常: {e}")
            raise

