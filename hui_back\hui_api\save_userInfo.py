"""
保存用户信息
"""
from ..hui_common.base_api import OnlineBaseSaasApi
from ..hui_common.time_utils import get_current_timestamp
from faker import Faker
fake = Faker(locale='zh_CN') # 生成中文文本



class Save_userInfo(OnlineBaseSaasApi):
    """保存用户信息API类"""

    def send_request(self, **kwargs):
        """通用方法实现：配置 + 刷新变量 + 发送请求"""
        # 获取配置并一次性完成所有设置
        config = self.configInfo


        # 配置API - 使用新的URL构建逻辑
        self.url = self.build_url('save_userInfo_url', 'save_userInfo_path')
        self.method, self.headers, self.json = 'post', {'content-type': 'application/x-www-form-urlencoded'}, None


        self.data = {
            'uid': '${uid}',
            'customerFields': '',
            'sender': config.get('Y', ''),
            'content': fake.sentence(),
            'userId': '${userId}',
            'tel': '[{"encrypt":"13000000000","desensitization":"13000000000","unDesensitization":"13000000000"}]',
            'email': '<EMAIL>',
            'uname': fake.name(), 
            'realname': fake.name(),
            'qq': '19000000000',
            'remark': '备注',
            'face': '',
            'weixin': '',
            'weibo': '',
            'sex': '',
            'age': '',
            'birthday': '',
            'partnerId': '',
            'createSource': '',
            'tid': config.get('Y', ''),
            'proviceId': '',
            'proviceName': '',
            'cityId': '',
            'cityName': '',
            'areaId': '',
            'areaName': '',
            'enterpriseId': '',
            'enterpriseName': '',
            'countryId': '',
            'countryName': '',
            'isVip': '',
            'vipLevel': '',
            'userLabel': ''



        }

        # 刷新变量并发送请求
        self.refresh_data_with_extracted_params('uid','userId')

        # # 获取真实uid并生成msgId
        # uid = self.data.get('uid', '')
        # msg_id = f"{uid}{get_current_timestamp()}"
        # self.data['msgId'] = msg_id

        # 发送请求并处理会话断开错误
        try:
            response = super().send_request(**kwargs)

            # 检查响应是否包含会话断开错误
            if response and hasattr(response, 'resp') and response.resp:
                try:
                    import json
                    resp_data = json.loads(response.resp.text)
                    if resp_data.get('retCode') == '210029':
                        self.logger.warning("检测到会话断开错误，建议重新初始化用户会话")
                        # 可以在这里添加自动重试逻辑
                except (json.JSONDecodeError, AttributeError):
                    pass  # 如果无法解析响应，继续正常流程

            return response

        except Exception as e:
            self.logger.error(f"发送请求时发生异常: {e}")
            raise

