import os
import allure
import pytest
import json
from pytest_assume.plugin import assume
from ..hui_api.save_userInfo import Save_userInfo
from ..hui_common.base_api import OnlineBaseSaasApi
from ..hui_common.env_manager import EnvManager
from hui_common.utils import parse_json_response

"""测试保存用户信息"""

@allure.epic('保存用户信息')
@allure.feature('保存用户信息')
class Test_Save_userInfo():
    @pytest.mark.order(9)  # 添加顺序标记，数字越小优先级越高
    @allure.story('保存用户信息')
    @allure.title('保存用户信息成功')
    def test_save_userInfo(self):
        with allure.step("保存用户信息"):
            # 确保用户已初始化，获取 uid 和 cid
            from ..hui_api.user_init import UserInit

            # 先进行用户初始化，确保 uid 和 cid 被提取
            user_init = UserInit()
            init_resp = user_init.send_request()

            print(f"用户初始化完成，提取的 uid: {user_init.uid}")
            print(f"用户初始化完成，提取的 cid: {user_init.cid}")

            # 创建客服发送消息实例
            save_userInfo = Save_userInfo()

            resp = save_userInfo.send_request()

            # 解析json响应
            resp_json = parse_json_response(resp)

            # 断言响应成功
            assume(resp.status_code == 200, f'预期状态码不对，预期:200，实际:{resp.status_code}')
            
        

if __name__ == '__main__':
    pytest.main([__file__])  # 使用 __file__ 来引用当前文件