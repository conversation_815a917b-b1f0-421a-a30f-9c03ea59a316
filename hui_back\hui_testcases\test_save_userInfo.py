import os
import allure
import pytest
import json
from pytest_assume.plugin import assume
from ..hui_api.save_userInfo import Save_userInfo
from ..hui_common.base_api import OnlineBaseSaasApi
from ..hui_common.env_manager import EnvManager
from hui_common.utils import parse_json_response

"""测试保存用户信息"""

@allure.epic('保存用户信息')
@allure.feature('保存用户信息')
class Test_Save_userInfo():
    @pytest.mark.order(4)  # 紧跟在用户初始化之后执行
    @allure.story('保存用户信息')
    @allure.title('保存用户信息成功')
    def test_save_userInfo(self):
        with allure.step("保存用户信息"):
            # 确保用户已初始化，获取 uid 和 cid
            from ..hui_api.user_init import UserInit

            # 先进行用户初始化，确保 uid 和 cid 被提取
            user_init = UserInit()
            init_resp = user_init.send_request()

            print(f"用户初始化完成，提取的 uid: {user_init.uid}")
            print(f"用户初始化完成，提取的 cid: {user_init.cid}")

            # 检查初始化是否成功
            if not user_init.uid or not user_init.variables_manager.get_variable('userId'):
                pytest.fail("用户初始化失败，无法获取必要的用户标识")

            # 创建保存用户信息实例，使用相同的变量管理器确保会话一致性
            save_userInfo = Save_userInfo()

            # 确保使用相同的会话状态
            save_userInfo.variables_manager = user_init.variables_manager
            save_userInfo.session = user_init.session  # 使用相同的session对象

            resp = save_userInfo.send_request()

            # 解析json响应
            resp_json = parse_json_response(resp)

            # 检查是否是会话断开错误
            if resp_json and resp_json.get('retCode') == '210029':
                print("检测到会话断开错误，尝试重新初始化...")

                # 重新初始化用户
                user_init_retry = UserInit()
                init_retry_resp = user_init_retry.send_request()

                # 更新保存用户信息实例的会话状态
                save_userInfo.variables_manager = user_init_retry.variables_manager
                save_userInfo.session = user_init_retry.session

                # 重新尝试保存用户信息
                resp = save_userInfo.send_request()
                resp_json = parse_json_response(resp)

                print("重新初始化后的响应:", resp_json)

            # 断言响应成功
            assume(resp.status_code == 200, f'预期状态码不对，预期:200，实际:{resp.status_code}')

            # 检查业务逻辑是否成功
            if resp_json:
                print(f"响应内容: {resp_json}")
                # 如果仍然是会话断开错误，则测试失败
                if resp_json.get('retCode') == '210029':
                    pytest.fail(f"会话断开错误未解决: {resp_json.get('retMsg')}")
                # 检查其他可能的错误
                elif resp_json.get('retCode') and resp_json.get('retCode') != '0':
                    print(f"警告：业务返回错误码 {resp_json.get('retCode')}: {resp_json.get('retMsg')}")
            else:
                print("警告：无法解析响应为JSON格式")
            
        

if __name__ == '__main__':
    pytest.main([__file__])  # 使用 __file__ 来引用当前文件