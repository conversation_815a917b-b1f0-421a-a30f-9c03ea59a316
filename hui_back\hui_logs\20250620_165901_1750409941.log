[2025-06-20 16:59:01,075] [INFO] [apitest] - #######################################################################
[2025-06-20 16:59:01,075] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "companyName": "liuyh",
  "uid": "56de2146104f480d8418d5400c978305",
  "cid": "796bc43f3e5245a8a04413161df96fdc",
  "puid": "faaeba3667c94efa9d5a7c8145a0ed9f",
  "userId": null,
  "companyId": "bdbe5660b2eb45758739527fda974091",
  "accountStatus": 3,
  "type": 3
}
[2025-06-20 16:59:01,076] [INFO] [apitest] - 通过【$.userId】提取到的结果是:None
[2025-06-20 16:59:01,077] [INFO] [apitest] - #######################################################################


[2025-06-20 16:59:01,077] [INFO] [apitest] - 通过路径 $.userId 没有找到userId，尝试使用extract_userId方法搜索更多路径
[2025-06-20 16:59:01,079] [INFO] [apitest] - #######################################################################
[2025-06-20 16:59:01,079] [INFO] [apitest] - 尝试提取userId，响应内容: {"robotSwitchFlag": 0, "companyName": "liuyh", "uid": "56de2146104f480d8418d5400c978305", "cid": "796bc43f3e5245a8a04413161df96fdc", "puid": "faaeba3667c94efa9d5a7c8145a0ed9f", "userId": null, "compan...
[2025-06-20 16:59:01,080] [INFO] [apitest] - 响应中顶级字段: ['robotSwitchFlag', 'companyName', 'uid', 'cid', 'puid', 'userId', 'companyId', 'accountStatus', 'type']
[2025-06-20 16:59:01,081] [INFO] [apitest] - userId字段为null或不存在，尝试智能回退策略
[2025-06-20 16:59:01,082] [INFO] [apitest] - 开始尝试userId智能回退策略...
[2025-06-20 16:59:01,082] [INFO] [apitest] - 回退策略【uid字段】成功: 在路径 $.uid 找到值 56de2146104f480d8418d5400c978305
[2025-06-20 16:59:01,083] [INFO] [apitest] - 已将回退userId值 56de2146104f480d8418d5400c978305 保存为变量 $userId
[2025-06-20 16:59:01,083] [INFO] [apitest] - #######################################################################


[2025-06-20 16:59:01,084] [INFO] [apitest] - 成功通过extract_userId方法找到值: 56de2146104f480d8418d5400c978305
[2025-06-20 16:59:01,085] [INFO] [apitest] - #######################################################################
[2025-06-20 16:59:01,085] [INFO] [apitest] - 尝试提取userId，响应内容: {"robotSwitchFlag": 0, "companyName": "liuyh", "uid": "56de2146104f480d8418d5400c978305", "cid": "796bc43f3e5245a8a04413161df96fdc", "puid": "faaeba3667c94efa9d5a7c8145a0ed9f", "userId": null, "compan...
[2025-06-20 16:59:01,086] [INFO] [apitest] - 响应中顶级字段: ['robotSwitchFlag', 'companyName', 'uid', 'cid', 'puid', 'userId', 'companyId', 'accountStatus', 'type']
[2025-06-20 16:59:01,087] [INFO] [apitest] - userId字段为null或不存在，尝试智能回退策略
[2025-06-20 16:59:01,089] [INFO] [apitest] - 开始尝试userId智能回退策略...
[2025-06-20 16:59:01,090] [INFO] [apitest] - 回退策略【uid字段】成功: 在路径 $.uid 找到值 56de2146104f480d8418d5400c978305
[2025-06-20 16:59:01,092] [INFO] [apitest] - 已将回退userId值 56de2146104f480d8418d5400c978305 保存为变量 $userId2
[2025-06-20 16:59:01,092] [INFO] [apitest] - #######################################################################


[2025-06-20 16:59:01,093] [INFO] [apitest] - #######################################################################
[2025-06-20 16:59:01,094] [INFO] [apitest] - 尝试提取userId，响应内容: {"robotSwitchFlag": 0, "companyName": "liuyh", "uid": "56de2146104f480d8418d5400c978305", "cid": "796bc43f3e5245a8a04413161df96fdc", "puid": "faaeba3667c94efa9d5a7c8145a0ed9f", "userId": null, "compan...
[2025-06-20 16:59:01,095] [INFO] [apitest] - 响应中顶级字段: ['robotSwitchFlag', 'companyName', 'uid', 'cid', 'puid', 'userId', 'companyId', 'accountStatus', 'type']
[2025-06-20 16:59:01,096] [WARNING] [apitest] - 无法从响应中提取userId，请检查响应格式或手动指定正确的JSONPath
[2025-06-20 16:59:01,096] [INFO] [apitest] - #######################################################################


