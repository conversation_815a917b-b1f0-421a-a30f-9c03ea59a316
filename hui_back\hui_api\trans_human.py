"""
用户转人工接口
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class TransHuman(OnlineBaseSaasApi):
    """转人工API类 """

    def send_request(self, **kwargs):
        """通用方法实现：配置 + 刷新变量 + 发送请求"""
        # 获取配置并一次性完成所有设置
        config = self.configInfo

        # 配置API - 使用新的URL构建逻辑
        self.url = self.build_url('trans_human_url', 'trans_human_path')
        self.method, self.headers, self.json = 'post', {'content-type': 'application/x-www-form-urlencoded'}, None
        self.data = {
            'sysNum': config.get('sysNum', ''),
            'chooseAdminId': '',
            'tranFlag': '0',
            'current': 'false',
            'groupId': '',
            'transferType': '0',
            'summaryParams': '',
            'transferAction': '',
            'flowType': '',
            'flowCompanyId': '',
            'flowGroupId': '',
            'activeTransfer': '1',
            'unknownQuestion': '',
            'docId': '',
            'adminHelloWord': 'null',
            'uid': '${uid}',  # 使用变量引用，自动替换
            'cid': '${cid}'   # 使用变量引用，自动替换
        }

        # 刷新变量并发送请求
        self.refresh_data_with_extracted_params('uid', 'cid')
        return super().send_request(**kwargs)
