[2025-06-20 17:31:43,172] [INFO] [pytest_result_log] - Start: hui_testcases/test_save_userInfo.py::Test_Save_userInfo::test_save_userInfo
[2025-06-20 17:31:43,286] [INFO] [apitest] - 【测试环境】开始设置测试环境
[2025-06-20 17:31:43,287] [INFO] [apitest] - 【环境选择】使用环境: ali
[2025-06-20 17:31:43,288] [INFO] [apitest] - 【登录流程】开始登录到 ali 环境
[2025-06-20 17:31:43,288] [INFO] [root] - 【快捷登录】使用快捷函数登录到 ali 环境
[2025-06-20 17:31:43,289] [INFO] [root] - 【登录操作】尝试登录到 ali 环境
[2025-06-20 17:31:43,290] [INFO] [root] - 【环境配置】开始加载 ali 环境配置
[2025-06-20 17:31:43,291] [DEBUG] [root] - 配置文件路径: C:\Users\<USER>\Downloads\API_Online-master-26 - 副本\hui_back\hui_config\hui_online_common.yml
[2025-06-20 17:31:43,291] [DEBUG] [root] - 读取YAML文件: C:\Users\<USER>\Downloads\API_Online-master-26 - 副本\hui_back\hui_config\hui_online_common.yml
[2025-06-20 17:31:43,299] [INFO] [root] - YAML配置中的环境节点: ali
[2025-06-20 17:31:43,299] [INFO] [root] - 成功加载 ali 环境配置
[2025-06-20 17:31:43,299] [INFO] [root] - 【环境节点】ali 环境信息:
[2025-06-20 17:31:43,299] [INFO] [root] -   - URL: http://api-c.sobot.com
[2025-06-20 17:31:43,299] [INFO] [root] -   - 路径: /basic-login/account/consoleLogin/4
[2025-06-20 17:31:43,301] [INFO] [root] -   - 登录用户: <EMAIL>
[2025-06-20 17:31:43,301] [DEBUG] [hui_back.hui_common.v6_console_login] - Current headers before update: {}
[2025-06-20 17:31:43,302] [INFO] [root] - 设置 content-type: application/json (域名: http://api-c.sobot.com)
[2025-06-20 17:31:43,302] [DEBUG] [hui_back.hui_common.v6_console_login] - Headers after update: {'content-type': 'application/json'}
[2025-06-20 17:31:43,302] [DEBUG] [root] - 登录参数: user=<EMAIL>, flag=1, terminalCode=2
[2025-06-20 17:31:43,302] [DEBUG] [root] - 额外参数: {}
[2025-06-20 17:31:43,302] [DEBUG] [root] - 请求数据格式: JSON
[2025-06-20 17:31:43,302] [INFO] [root] - 【API请求】发送登录请求到: http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-20 17:31:43,303] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################
[2025-06-20 17:31:43,303] [INFO] [hui_back.hui_common.v6_console_login] - 请求方法：post
[2025-06-20 17:31:43,303] [INFO] [hui_back.hui_common.v6_console_login] - 请求地址：http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-20 17:31:43,304] [INFO] [hui_back.hui_common.v6_console_login] - 请求参数 json：{'loginUser': '<EMAIL>', 'loginPwd': 'bGl1eWgxMjM=', 'loginFlag': 1, 'terminalCode': 2}
[2025-06-20 17:31:44,021] [INFO] [hui_back.hui_common.v6_console_login] - 响应码：200
[2025-06-20 17:31:44,022] [INFO] [hui_back.hui_common.v6_console_login] - 响应体：{"companyId":"bdbe5660b2eb45758739527fda974091","item":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************.g7G-sdXsc-4h_RZHgVauwxTb9vqcd12PZONgqRLRRXg","items":[],"loginCategory":"console,serviceConsole,appConsole","newConsoleFlag":1,"retCode":"000000","retMsg":"操作成功","zone":1}
[2025-06-20 17:31:44,022] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################


[2025-06-20 17:31:44,023] [INFO] [root] - 【API响应】请求完成，耗时: 0.72秒，状态码: 200
[2025-06-20 17:31:44,023] [INFO] [root] - 【响应验证】验证成功，返回码: 000000
[2025-06-20 17:31:44,024] [DEBUG] [root] - 【响应JSON】: {"companyId": "bdbe5660b2eb45758739527fda974091", "item": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJj...
[2025-06-20 17:31:44,024] [DEBUG] [root] - 在 item 字符串中找到会话信息
[2025-06-20 17:31:44,024] [INFO] [root] - 【会话信息】成功提取会话信息: eyJ0eXAiOiJKV1Q...
[2025-06-20 17:31:44,024] [INFO] [root] - 【登录结果】登录成功，获取到会话信息
[2025-06-20 17:31:44,025] [INFO] [apitest] - 【登录成功】环境: ali, 会话ID: eyJ0eXAiOiJKV1Q...
[2025-06-20 17:31:44,026] [INFO] [apitest] - 【测试准备】环境设置完成，测试前登录成功
[2025-06-20 17:31:44,028] [INFO] [apitest] - #######################################################################
[2025-06-20 17:31:44,029] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:31:44,030] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-20 17:31:44,031] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=default_1750411903_e686e00d&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=default_1750411903_e686e00d
[2025-06-20 17:31:45,182] [INFO] [apitest] - 响应码：200
[2025-06-20 17:31:45,182] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":"7cff3c72501a4edabe4f344884af88a9","userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"b53c1b81a1bf48639a998b16676d627d:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":1,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":1,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"060fdc03396b48b8b601d867c7a37229","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":1,"privacyAgree":1,"leaveExplain":null,"puid":"897802fca69f4d9b8a716ac67caa05e7","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-20 17:31:45,186] [INFO] [apitest] - #######################################################################


[2025-06-20 17:31:45,187] [INFO] [apitest] - #######################################################################
[2025-06-20 17:31:45,189] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:31:45,192] [INFO] [apitest] - 通过【$.cid】提取到的结果是:060fdc03396b48b8b601d867c7a37229
[2025-06-20 17:31:45,193] [INFO] [apitest] - #######################################################################


[2025-06-20 17:31:45,193] [INFO] [apitest] - 将提取的值【060fdc03396b48b8b601d867c7a37229】保存为变量【cid】
[2025-06-20 17:31:45,194] [INFO] [apitest] - #######################################################################
[2025-06-20 17:31:45,197] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:31:45,198] [INFO] [apitest] - 通过【$.uid】提取到的结果是:56de2146104f480d8418d5400c978305
[2025-06-20 17:31:45,199] [INFO] [apitest] - #######################################################################


[2025-06-20 17:31:45,202] [INFO] [apitest] - 将提取的值【56de2146104f480d8418d5400c978305】保存为变量【uid】
[2025-06-20 17:31:45,203] [INFO] [apitest] - #######################################################################
[2025-06-20 17:31:45,204] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:31:45,206] [INFO] [apitest] - 通过【$.puid】提取到的结果是:897802fca69f4d9b8a716ac67caa05e7
[2025-06-20 17:31:45,207] [INFO] [apitest] - #######################################################################


[2025-06-20 17:31:45,208] [INFO] [apitest] - 将提取的值【897802fca69f4d9b8a716ac67caa05e7】保存为变量【puid】
[2025-06-20 17:31:45,209] [INFO] [apitest] - #######################################################################
[2025-06-20 17:31:45,211] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:31:45,213] [INFO] [apitest] - 通过【$.userId】提取到的结果是:7cff3c72501a4edabe4f344884af88a9
[2025-06-20 17:31:45,213] [INFO] [apitest] - #######################################################################


[2025-06-20 17:31:45,214] [INFO] [apitest] - 将提取的值【7cff3c72501a4edabe4f344884af88a9】保存为变量【userId】
[2025-06-20 17:31:45,218] [INFO] [apitest] - #######################################################################
[2025-06-20 17:31:45,219] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:31:45,220] [INFO] [apitest] - 请求地址：https://api-c.soboten.com/text/chat-kwb/admin/addCustomerInfo.action
[2025-06-20 17:31:45,220] [INFO] [apitest] - 请求参数 data：uid=56de2146104f480d8418d5400c978305&customerFields=&sender=xOh8P5f7pLDPHW5kyWJscuzHfKg8v3Wj5TWF2jP6WSj%2BIBS1zqXJr8fZ4ny5vE8t&content=%E5%85%B3%E4%BA%8E%E9%98%85%E8%AF%BB%E6%8E%A7%E5%88%B6%E5%8A%A0%E5%85%A5%E5%AF%B9%E4%BA%8E.&userId=7cff3c72501a4edabe4f344884af88a9&tel=%5B%7B%22encrypt%22%3A%2213000000000%22%2C%22desensitization%22%3A%2213000000000%22%2C%22unDesensitization%22%3A%2213000000000%22%7D%5D&email=19000000000%40163.com&uname=%E8%90%A7%E5%8D%8E&realname=%E6%9D%8E%E4%BC%9F&qq=19000000000&remark=%E5%A4%87%E6%B3%A8&face=&weixin=&weibo=&sex=&age=&birthday=&partnerId=&createSource=&tid=xOh8P5f7pLDPHW5kyWJscuzHfKg8v3Wj5TWF2jP6WSj%2BIBS1zqXJr8fZ4ny5vE8t&proviceId=&proviceName=&cityId=&cityName=&areaId=&areaName=&enterpriseId=&enterpriseName=&countryId=&countryName=&isVip=&vipLevel=&userLabel=&cid=060fdc03396b48b8b601d867c7a37229
[2025-06-20 17:31:45,325] [ERROR] [apitest] - 请求发送出错：('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
[2025-06-20 17:31:46,121] [ERROR] [pytest_result_log] - test status is FAILED (hui_testcases/test_save_userInfo.py::Test_Save_userInfo::test_save_userInfo): ConnectionError
[2025-06-20 17:31:46,122] [DEBUG] [pytest_result_log] - hui_testcases/test_save_userInfo.py::Test_Save_userInfo::test_save_userInfo -> self = <urllib3.connectionpool.HTTPSConnectionPool object at 0x0000019D15BE3410>, method = 'POST', url = '/text/chat-kwb/admin/addCustomerInfo.action'
body = 'uid=56de2146104f480d8418d5400c978305&customerFields=&sender=xOh8P5f7pLDPHW5kyWJscuzHfKg8v3Wj5TWF2jP6WSj%2BIBS1zqXJr8f...enterpriseId=&enterpriseName=&countryId=&countryName=&isVip=&vipLevel=&userLabel=&cid=060fdc03396b48b8b601d867c7a37229'
headers = {'User-Agent': 'python-requests/2.32.3', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept': '*/*', 'Connection': 'keep-alive', 'content-type': 'application/x-www-form-urlencoded', 'Content-Length': '844'}
retries = Retry(total=0, connect=None, read=False, redirect=None, status=None), redirect = False, assert_same_host = False, timeout = Timeout(connect=None, read=None, total=None)
pool_timeout = None, release_conn = False, chunked = False, body_pos = None, preload_content = False, decode_content = False, response_kw = {}
parsed_url = Url(scheme=None, auth=None, host=None, port=None, path='/text/chat-kwb/admin/addCustomerInfo.action', query=None, fragment=None), destination_scheme = None
conn = None, release_this_conn = True, http_tunnel_required = True, err = None, clean_exit = False

    def urlopen(  # type: ignore[override]
        self,
        method: str,
        url: str,
        body: _TYPE_BODY | None = None,
        headers: typing.Mapping[str, str] | None = None,
        retries: Retry | bool | int | None = None,
        redirect: bool = True,
        assert_same_host: bool = True,
        timeout: _TYPE_TIMEOUT = _DEFAULT_TIMEOUT,
        pool_timeout: int | None = None,
        release_conn: bool | None = None,
        chunked: bool = False,
        body_pos: _TYPE_BODY_POSITION | None = None,
        preload_content: bool = True,
        decode_content: bool = True,
        **response_kw: typing.Any,
    ) -> BaseHTTPResponse:
        """
        Get a connection from the pool and perform an HTTP request. This is the
        lowest level call for making a request, so you'll need to specify all
        the raw details.
    
        .. note::
    
           More commonly, it's appropriate to use a convenience method
           such as :meth:`request`.
    
        .. note::
    
           `release_conn` will only behave as expected if
           `preload_content=False` because we want to make
           `preload_content=False` the default behaviour someday soon without
           breaking backwards compatibility.
    
        :param method:
            HTTP request method (such as GET, POST, PUT, etc.)
    
        :param url:
            The URL to perform the request on.
    
        :param body:
            Data to send in the request body, either :class:`str`, :class:`bytes`,
            an iterable of :class:`str`/:class:`bytes`, or a file-like object.
    
        :param headers:
            Dictionary of custom headers to send, such as User-Agent,
            If-None-Match, etc. If None, pool headers are used. If provided,
            these headers completely replace any pool-specific headers.
    
        :param retries:
            Configure the number of retries to allow before raising a
            :class:`~urllib3.exceptions.MaxRetryError` exception.
    
            Pass ``None`` to retry until you receive a response. Pass a
            :class:`~urllib3.util.retry.Retry` object for fine-grained control
            over different types of retries.
            Pass an integer number to retry connection errors that many times,
            but no other types of errors. Pass zero to never retry.
    
            If ``False``, then retries are disabled and any exception is raised
            immediately. Also, instead of raising a MaxRetryError on redirects,
            the redirect response will be returned.
    
        :type retries: :class:`~urllib3.util.retry.Retry`, False, or an int.
    
        :param redirect:
            If True, automatically handle redirects (status codes 301, 302,
            303, 307, 308). Each redirect counts as a retry. Disabling retries
            will disable redirect, too.
    
        :param assert_same_host:
            If ``True``, will make sure that the host of the pool requests is
            consistent else will raise HostChangedError. When ``False``, you can
            use the pool on an HTTP proxy and request foreign hosts.
    
        :param timeout:
            If specified, overrides the default timeout for this one
            request. It may be a float (in seconds) or an instance of
            :class:`urllib3.util.Timeout`.
    
        :param pool_timeout:
            If set and the pool is set to block=True, then this method will
            block for ``pool_timeout`` seconds and raise EmptyPoolError if no
            connection is available within the time period.
    
        :param bool preload_content:
            If True, the response's body will be preloaded into memory.
    
        :param bool decode_content:
            If True, will attempt to decode the body based on the
            'content-encoding' header.
    
        :param release_conn:
            If False, then the urlopen call will not release the connection
            back into the pool once a response is received (but will release if
            you read the entire contents of the response such as when
            `preload_content=True`). This is useful if you're not preloading
            the response's content immediately. You will need to call
            ``r.release_conn()`` on the response ``r`` to return the connection
            back into the pool. If None, it takes the value of ``preload_content``
            which defaults to ``True``.
    
        :param bool chunked:
            If True, urllib3 will send the body using chunked transfer
            encoding. Otherwise, urllib3 will send the body using the standard
            content-length form. Defaults to False.
    
        :param int body_pos:
            Position to seek to in file-like body in the event of a retry or
            redirect. Typically this won't need to be set because urllib3 will
            auto-populate the value when needed.
        """
        parsed_url = parse_url(url)
        destination_scheme = parsed_url.scheme
    
        if headers is None:
            headers = self.headers
    
        if not isinstance(retries, Retry):
            retries = Retry.from_int(retries, redirect=redirect, default=self.retries)
    
        if release_conn is None:
            release_conn = preload_content
    
        # Check host
        if assert_same_host and not self.is_same_host(url):
            raise HostChangedError(self, url, retries)
    
        # Ensure that the URL we're connecting to is properly encoded
        if url.startswith("/"):
            url = to_str(_encode_target(url))
        else:
            url = to_str(parsed_url.url)
    
        conn = None
    
        # Track whether `conn` needs to be released before
        # returning/raising/recursing. Update this variable if necessary, and
        # leave `release_conn` constant throughout the function. That way, if
        # the function recurses, the original value of `release_conn` will be
        # passed down into the recursive call, and its value will be respected.
        #
        # See issue #651 [1] for details.
        #
        # [1] <https://github.com/urllib3/urllib3/issues/651>
        release_this_conn = release_conn
    
        http_tunnel_required = connection_requires_http_tunnel(
            self.proxy, self.proxy_config, destination_scheme
        )
    
        # Merge the proxy headers. Only done when not using HTTP CONNECT. We
        # have to copy the headers dict so we can safely change it without those
        # changes being reflected in anyone else's copy.
        if not http_tunnel_required:
            headers = headers.copy()  # type: ignore[attr-defined]
            headers.update(self.proxy_headers)  # type: ignore[union-attr]
    
        # Must keep the exception bound to a separate variable or else Python 3
        # complains about UnboundLocalError.
        err = None
    
        # Keep track of whether we cleanly exited the except block. This
        # ensures we do proper cleanup in finally.
        clean_exit = False
    
        # Rewind body position, if needed. Record current position
        # for future rewinds in the event of a redirect/retry.
        body_pos = set_file_position(body, body_pos)
    
        try:
            # Request a connection from the queue.
            timeout_obj = self._get_timeout(timeout)
            conn = self._get_conn(timeout=pool_timeout)
    
            conn.timeout = timeout_obj.connect_timeout  # type: ignore[assignment]
    
            # Is this a closed/new connection that requires CONNECT tunnelling?
            if self.proxy is not None and http_tunnel_required and conn.is_closed:
                try:
>                   self._prepare_proxy(conn)

..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\connectionpool.py:776: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\connectionpool.py:1045: in _prepare_proxy
    conn.connect()
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\connection.py:642: in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\connection.py:782: in _ssl_wrap_socket_and_match_hostname
    ssl_sock = ssl_wrap_socket(
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\util\ssl_.py:470: in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(sock, context, tls_in_tls, server_hostname)
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\util\ssl_.py:514: in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock, server_hostname=server_hostname)
..\..\..\AppData\Local\Programs\Python\Python311\Lib\ssl.py:517: in wrap_socket
    return self.sslsocket_class._create(
..\..\..\AppData\Local\Programs\Python\Python311\Lib\ssl.py:1104: in _create
    self.do_handshake()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = <ssl.SSLSocket [closed] fd=-1, family=2, type=1, proto=0>, block = False

    @_sslcopydoc
    def do_handshake(self, block=False):
        self._check_connected()
        timeout = self.gettimeout()
        try:
            if timeout == 0.0 and block:
                self.settimeout(None)
>           self._sslobj.do_handshake()
E           ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

..\..\..\AppData\Local\Programs\Python\Python311\Lib\ssl.py:1382: ConnectionResetError

During handling of the above exception, another exception occurred:

self = <requests.adapters.HTTPAdapter object at 0x0000019D15BD4210>, request = <PreparedRequest [POST]>, stream = False, timeout = Timeout(connect=None, read=None, total=None)
verify = True, cert = None, proxies = OrderedDict([('http', 'http://127.0.0.1:9999'), ('https', 'http://127.0.0.1:9999'), ('ftp', 'http://127.0.0.1:9999')])

    def send(
        self, request, stream=False, timeout=None, verify=True, cert=None, proxies=None
    ):
        """Sends PreparedRequest object. Returns Response object.
    
        :param request: The :class:`PreparedRequest <PreparedRequest>` being sent.
        :param stream: (optional) Whether to stream the request content.
        :param timeout: (optional) How long to wait for the server to send
            data before giving up, as a float, or a :ref:`(connect timeout,
            read timeout) <timeouts>` tuple.
        :type timeout: float or tuple or urllib3 Timeout object
        :param verify: (optional) Either a boolean, in which case it controls whether
            we verify the server's TLS certificate, or a string, in which case it
            must be a path to a CA bundle to use
        :param cert: (optional) Any user-provided SSL certificate to be trusted.
        :param proxies: (optional) The proxies dictionary to apply to the request.
        :rtype: requests.Response
        """
    
        try:
            conn = self.get_connection_with_tls_context(
                request, verify, proxies=proxies, cert=cert
            )
        except LocationValueError as e:
            raise InvalidURL(e, request=request)
    
        self.cert_verify(conn, request.url, verify, cert)
        url = self.request_url(request, proxies)
        self.add_headers(
            request,
            stream=stream,
            timeout=timeout,
            verify=verify,
            cert=cert,
            proxies=proxies,
        )
    
        chunked = not (request.body is None or "Content-Length" in request.headers)
    
        if isinstance(timeout, tuple):
            try:
                connect, read = timeout
                timeout = TimeoutSauce(connect=connect, read=read)
            except ValueError:
                raise ValueError(
                    f"Invalid timeout {timeout}. Pass a (connect, read) timeout tuple, "
                    f"or a single float to set both timeouts to the same value."
                )
        elif isinstance(timeout, TimeoutSauce):
            pass
        else:
            timeout = TimeoutSauce(connect=timeout, read=timeout)
    
        try:
>           resp = conn.urlopen(
                method=request.method,
                url=url,
                body=request.body,
                headers=request.headers,
                redirect=False,
                assert_same_host=False,
                preload_content=False,
                decode_content=False,
                retries=self.max_retries,
                timeout=timeout,
                chunked=chunked,
            )

..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\requests\adapters.py:667: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\connectionpool.py:844: in urlopen
    retries = retries.increment(
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\util\retry.py:470: in increment
    raise reraise(type(error), error, _stacktrace)
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\util\util.py:38: in reraise
    raise value.with_traceback(tb)
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\connectionpool.py:776: in urlopen
    self._prepare_proxy(conn)
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\connectionpool.py:1045: in _prepare_proxy
    conn.connect()
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\connection.py:642: in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\connection.py:782: in _ssl_wrap_socket_and_match_hostname
    ssl_sock = ssl_wrap_socket(
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\util\ssl_.py:470: in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(sock, context, tls_in_tls, server_hostname)
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\urllib3\util\ssl_.py:514: in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock, server_hostname=server_hostname)
..\..\..\AppData\Local\Programs\Python\Python311\Lib\ssl.py:517: in wrap_socket
    return self.sslsocket_class._create(
..\..\..\AppData\Local\Programs\Python\Python311\Lib\ssl.py:1104: in _create
    self.do_handshake()
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = <ssl.SSLSocket [closed] fd=-1, family=2, type=1, proto=0>, block = False

    @_sslcopydoc
    def do_handshake(self, block=False):
        self._check_connected()
        timeout = self.gettimeout()
        try:
            if timeout == 0.0 and block:
                self.settimeout(None)
>           self._sslobj.do_handshake()
E           urllib3.exceptions.ProtocolError: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))

..\..\..\AppData\Local\Programs\Python\Python311\Lib\ssl.py:1382: ProtocolError

During handling of the above exception, another exception occurred:

self = <hui_back.hui_testcases.test_save_userInfo.Test_Save_userInfo object at 0x0000019D15B2BC10>

    @pytest.mark.order(9)  # 添加顺序标记，数字越小优先级越高
    @allure.story('保存用户信息')
    @allure.title('保存用户信息成功')
    def test_save_userInfo(self):
        with allure.step("保存用户信息"):
            # 确保用户已初始化，获取 uid 和 cid
            from ..hui_api.user_init import UserInit
    
            # 先进行用户初始化，确保 uid 和 cid 被提取
            user_init = UserInit()
            init_resp = user_init.send_request()
    
            print(f"用户初始化完成，提取的 uid: {user_init.uid}")
            print(f"用户初始化完成，提取的 cid: {user_init.cid}")
    
            # 创建客服发送消息实例
            save_userInfo = Save_userInfo()
    
>           resp = save_userInfo.send_request()

hui_testcases\test_save_userInfo.py:34: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
hui_api\save_userInfo.py:73: in send_request
    return super().send_request(**kwargs)
hui_common\base_api.py:165: in send_request
    response = super().send_request(**kwargs)
hui_common\request_processor.py:34: in wrapper
    return original_send_request(self, **kwargs)
hui_common\request_client.py:175: in send_request
    self.resp = func(url, **kwargs)
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\requests\sessions.py:637: in post
    return self.request("POST", url, data=data, json=json, **kwargs)
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\requests\sessions.py:589: in request
    resp = self.send(prep, **send_kwargs)
..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\requests\sessions.py:703: in send
    r = adapter.send(request, **kwargs)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

self = <requests.adapters.HTTPAdapter object at 0x0000019D15BD4210>, request = <PreparedRequest [POST]>, stream = False, timeout = Timeout(connect=None, read=None, total=None)
verify = True, cert = None, proxies = OrderedDict([('http', 'http://127.0.0.1:9999'), ('https', 'http://127.0.0.1:9999'), ('ftp', 'http://127.0.0.1:9999')])

    def send(
        self, request, stream=False, timeout=None, verify=True, cert=None, proxies=None
    ):
        """Sends PreparedRequest object. Returns Response object.
    
        :param request: The :class:`PreparedRequest <PreparedRequest>` being sent.
        :param stream: (optional) Whether to stream the request content.
        :param timeout: (optional) How long to wait for the server to send
            data before giving up, as a float, or a :ref:`(connect timeout,
            read timeout) <timeouts>` tuple.
        :type timeout: float or tuple or urllib3 Timeout object
        :param verify: (optional) Either a boolean, in which case it controls whether
            we verify the server's TLS certificate, or a string, in which case it
            must be a path to a CA bundle to use
        :param cert: (optional) Any user-provided SSL certificate to be trusted.
        :param proxies: (optional) The proxies dictionary to apply to the request.
        :rtype: requests.Response
        """
    
        try:
            conn = self.get_connection_with_tls_context(
                request, verify, proxies=proxies, cert=cert
            )
        except LocationValueError as e:
            raise InvalidURL(e, request=request)
    
        self.cert_verify(conn, request.url, verify, cert)
        url = self.request_url(request, proxies)
        self.add_headers(
            request,
            stream=stream,
            timeout=timeout,
            verify=verify,
            cert=cert,
            proxies=proxies,
        )
    
        chunked = not (request.body is None or "Content-Length" in request.headers)
    
        if isinstance(timeout, tuple):
            try:
                connect, read = timeout
                timeout = TimeoutSauce(connect=connect, read=read)
            except ValueError:
                raise ValueError(
                    f"Invalid timeout {timeout}. Pass a (connect, read) timeout tuple, "
                    f"or a single float to set both timeouts to the same value."
                )
        elif isinstance(timeout, TimeoutSauce):
            pass
        else:
            timeout = TimeoutSauce(connect=timeout, read=timeout)
    
        try:
            resp = conn.urlopen(
                method=request.method,
                url=url,
                body=request.body,
                headers=request.headers,
                redirect=False,
                assert_same_host=False,
                preload_content=False,
                decode_content=False,
                retries=self.max_retries,
                timeout=timeout,
                chunked=chunked,
            )
    
        except (ProtocolError, OSError) as err:
>           raise ConnectionError(err, request=request)
E           requests.exceptions.ConnectionError: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))

..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\requests\adapters.py:682: ConnectionError
[2025-06-20 17:31:46,122] [INFO] [pytest_result_log] - End: hui_testcases/test_save_userInfo.py::Test_Save_userInfo::test_save_userInfo
[2025-06-20 17:31:46,123] [INFO] [apitest] - 【测试完成】测试会话结束，清理环境
