#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话管理器：处理客服系统的会话状态管理
"""

import json
import time
from typing import Optional, Dict, Any


class SessionManager:
    """客服系统会话管理器"""
    
    def __init__(self, logger=None):
        self.logger = logger
        self._session_data = {}
        self._last_init_time = None
    
    def validate_session_response(self, response) -> Dict[str, Any]:
        """
        验证响应中的会话状态
        :param response: API响应对象
        :return: 验证结果字典
        """
        result = {
            'is_valid': False,
            'error_code': None,
            'error_message': None,
            'session_expired': False,
            'need_reinit': False
        }
        
        if not response or not hasattr(response, 'resp'):
            result['error_message'] = '响应对象无效'
            return result
        
        # 检查HTTP状态码
        if response.resp.status_code != 200:
            result['error_code'] = response.resp.status_code
            result['error_message'] = f'HTTP错误: {response.resp.status_code}'
            if response.resp.status_code == 502:
                result['need_reinit'] = True
                result['error_message'] += ' (服务器网关错误，建议重新初始化)'
            return result
        
        # 检查响应内容
        if not response.resp.text:
            result['error_message'] = '响应内容为空'
            result['need_reinit'] = True
            return result
        
        # 解析JSON响应
        try:
            resp_data = json.loads(response.resp.text)
        except json.JSONDecodeError:
            result['error_message'] = '响应不是有效的JSON格式'
            return result
        
        # 检查业务错误码
        ret_code = resp_data.get('retCode')
        if ret_code == '210029':
            result['error_code'] = '210029'
            result['error_message'] = '用户已与客服断开连接'
            result['session_expired'] = True
            result['need_reinit'] = True
            return result
        
        # 如果没有错误，认为会话有效
        if ret_code in [None, '0', '000000', 'success']:
            result['is_valid'] = True
        else:
            result['error_code'] = ret_code
            result['error_message'] = resp_data.get('retMsg', '未知错误')
        
        return result
    
    def check_session_variables(self, variables_manager) -> Dict[str, Any]:
        """
        检查会话相关变量是否完整
        :param variables_manager: 变量管理器实例
        :return: 检查结果
        """
        result = {
            'is_complete': False,
            'missing_variables': [],
            'variables': {}
        }
        
        # 检查关键会话变量
        required_vars = ['uid', 'userId', 'cid', 'puid']
        
        for var_name in required_vars:
            value = variables_manager.get_variable(var_name)
            result['variables'][var_name] = value
            
            if not value or str(value).startswith('default_'):
                result['missing_variables'].append(var_name)
        
        result['is_complete'] = len(result['missing_variables']) == 0
        
        return result
    
    def diagnose_210029_error(self, response, variables_manager) -> str:
        """
        诊断210029错误的具体原因
        :param response: API响应
        :param variables_manager: 变量管理器
        :return: 诊断报告
        """
        report = ["=" * 60]
        report.append("210029错误诊断报告")
        report.append("=" * 60)
        
        # 1. 检查会话变量
        var_check = self.check_session_variables(variables_manager)
        report.append("\n1. 会话变量检查:")
        
        if var_check['is_complete']:
            report.append("  ✅ 所有关键变量都已设置")
            for var, value in var_check['variables'].items():
                report.append(f"    {var}: {value}")
        else:
            report.append("  ❌ 发现缺失的关键变量:")
            for var in var_check['missing_variables']:
                report.append(f"    缺失: {var}")
            report.append("  可用变量:")
            for var, value in var_check['variables'].items():
                if value:
                    report.append(f"    {var}: {value}")
        
        # 2. 分析可能原因
        report.append("\n2. 可能原因分析:")
        
        if var_check['missing_variables']:
            report.append("  🔍 主要原因：用户初始化失败")
            report.append("    - 用户初始化接口返回502错误")
            report.append("    - 关键会话变量未能正确提取")
            report.append("    - 服务器无法建立有效的用户会话")
        else:
            report.append("  🔍 可能原因：会话状态问题")
            report.append("    - 会话已超时")
            report.append("    - 服务器端会话被清理")
            report.append("    - 网络连接中断导致会话丢失")
        
        # 3. 解决建议
        report.append("\n3. 解决建议:")
        report.append("  💡 立即措施：")
        report.append("    1. 重新执行用户初始化接口")
        report.append("    2. 确保初始化接口返回200状态码")
        report.append("    3. 验证所有关键变量都被正确提取")
        report.append("    4. 在会话建立成功后再执行业务操作")
        
        report.append("\n  🔧 长期措施：")
        report.append("    1. 添加用户初始化的重试机制")
        report.append("    2. 实现会话状态的定期检查")
        report.append("    3. 增加会话超时的自动恢复")
        report.append("    4. 监控服务器502错误的频率")
        
        report.append("\n" + "=" * 60)
        
        return "\n".join(report)
    
    def suggest_recovery_actions(self, error_type: str) -> list:
        """
        根据错误类型建议恢复措施
        :param error_type: 错误类型
        :return: 恢复措施列表
        """
        actions = []
        
        if error_type == '210029':
            actions = [
                "重新执行用户初始化接口",
                "检查网络连接状态",
                "验证服务器可用性",
                "确认会话参数完整性",
                "实施重试机制"
            ]
        elif error_type == '502':
            actions = [
                "检查服务器状态",
                "等待服务器恢复",
                "尝试使用备用服务器",
                "联系运维团队",
                "实施降级策略"
            ]
        else:
            actions = [
                "检查请求参数",
                "验证API接口地址",
                "查看详细错误日志",
                "联系技术支持"
            ]
        
        return actions
