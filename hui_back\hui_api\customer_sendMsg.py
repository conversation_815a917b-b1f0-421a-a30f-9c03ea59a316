"""
客服发送消息
"""
from ..hui_common.base_api import OnlineBaseSaasApi
from ..hui_common.time_utils import get_current_timestamp
from faker import Faker
fake = Faker(locale='zh_CN') # 生成中文文本


class CustomerSendMsg(OnlineBaseSaasApi):
    """客服发送消息API类"""

    def send_request(self, **kwargs):
        """通用方法实现：配置 + 刷新变量 + 发送请求"""
        # 获取配置并一次性完成所有设置
        config = self.configInfo


        # 配置API - 使用新的URL构建逻辑
        self.url = self.build_url('customer_sendMsg_url', 'customer_sendMsg_path')
        self.method, self.headers, self.json = 'post', {'content-type': 'application/x-www-form-urlencoded'}, None

        # # 使用表达式求值器处理 msgId
        # msg_id_expression = '${uid}+get_current_timestamp()'
        # msg_id = self.expression_evaluator.evaluate_expression(msg_id_expression)

        self.data = {
            'uid': '${uid}',
            'cid': '${cid}',
            'tid': config.get('Y', ''),
            'msgType': 0,
            'content': fake.sentence(),
            'objMsgType': '',
            'docId': '',
            'replyId': '',
            'fileName': '',
            'msgId': '',  # 先留空，后面动态生成
            'title': '',
            'answerId': '',
            'resend': ''
        }

        # 刷新变量并发送请求
        self.refresh_data_with_extracted_params('uid', 'cid')

        # 获取真实uid并生成msgId
        uid = self.data.get('uid', '')
        msg_id = f"{uid}{get_current_timestamp()}"
        self.data['msgId'] = msg_id

        return super().send_request(**kwargs)

