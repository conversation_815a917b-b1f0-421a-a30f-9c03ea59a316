# 参数提取使用指南

## 🎯 **概述**

本指南介绍如何从用户初始化接口中提取参数（如 `partnerId`），并在其他接口中复用这些参数。

## 🔧 **实现方案**

### **1. 用户初始化接口的参数提取**

#### **A. 响应字段提取**
从API响应中提取关键字段：
```python
# 在 UserInit 类中
self.cid = response.extract_and_save('$.cid', 'cid')
self.uid = response.extract_and_save('$.uid', 'uid')
self.puid = response.extract_and_save('$.puid', 'puid')
self.userId = response.extract_and_save('$.userId', 'userId')
self.companyId = response.extract_and_save('$.companyId', 'companyId')
self.schemeId = response.extract_and_save('$.schemeId', 'schemeId')
```

#### **B. 请求参数提取**
从请求参数中提取有用的值：
```python
# 使用通用方法提取请求参数
request_params = self.extract_and_save_params([
    'partnerId', 'sysNum', 'agid', 'xst', 'source', 'language', 
    'ack', 'isReComment', 'newFlag', 'isJs'
])

# 设置实例属性以便直接访问
self.partnerId = request_params.get('partnerId')
self.sysNum = request_params.get('sysNum')
self.agid = request_params.get('agid')
```

### **2. 基础API类的通用方法**

#### **A. 单个参数提取**
```python
# 提取单个参数
partner_id = self.extract_request_params('partnerId')
```

#### **B. 多个参数提取**
```python
# 提取多个参数
params = self.extract_request_params(['partnerId', 'sysNum', 'agid'])
```

#### **C. 批量提取并保存**
```python
# 批量提取并保存到变量管理器
extracted = self.extract_and_save_params([
    'partnerId', 'sysNum', 'agid', 'xst'
])
```

### **3. 在其他接口中使用提取的参数**

#### **A. 直接使用变量引用**
```python
self.data = {
    'uid': '${uid}',                    # 从响应中提取
    'userId': '${userId}',              # 从响应中提取
    'partnerId': '${partnerId}',        # 从请求参数中提取
    'sysNum': '${sysNum}',              # 从请求参数中提取
    'agid': '${agid}',                  # 从请求参数中提取
    # ... 其他参数
}
```

#### **B. 刷新变量**
```python
# 刷新所有使用的变量
self.refresh_data_with_extracted_params(
    'uid', 'userId', 'partnerId', 'sysNum', 'agid', 'cid'
)
```

## 📋 **可提取的参数列表**

### **响应字段（从API响应中提取）**
| 参数名 | 描述 | 用途 |
|--------|------|------|
| `uid` | 用户ID | 会话标识 |
| `userId` | 用户标识 | 用户识别 |
| `cid` | 客服ID | 客服会话 |
| `puid` | 父用户ID | 用户关系 |
| `companyId` | 公司ID | 企业标识 |
| `schemeId` | 方案ID | 配置方案 |
| `visitSchemeId` | 访问方案ID | 访问配置 |

### **请求参数（从请求数据中提取）**
| 参数名 | 描述 | 用途 |
|--------|------|------|
| `partnerId` | 合作伙伴ID | 业务标识 |
| `sysNum` | 系统编号 | 系统标识 |
| `agid` | 代理ID | 代理标识 |
| `xst` | 扩展标识 | 扩展功能 |
| `source` | 来源 | 渠道标识 |
| `language` | 语言 | 本地化 |
| `ack` | 确认标识 | 确认机制 |
| `isReComment` | 是否重新评论 | 评论控制 |
| `newFlag` | 新标识 | 新用户标识 |
| `isJs` | 是否JS | 技术标识 |

## 🚀 **使用示例**

### **示例1：在保存用户信息接口中使用**
```python
class Save_userInfo(OnlineBaseSaasApi):
    def send_request(self, **kwargs):
        self.data = {
            'uid': '${uid}',                    # 从用户初始化响应提取
            'userId': '${userId}',              # 从用户初始化响应提取
            'partnerId': '${partnerId}',        # 从用户初始化请求提取
            'cid': '${cid}',                    # 从用户初始化响应提取
            # ... 其他参数
        }
        
        # 刷新变量
        self.refresh_data_with_extracted_params(
            'uid', 'userId', 'partnerId', 'cid'
        )
        
        return super().send_request(**kwargs)
```

### **示例2：在客服发送消息接口中使用**
```python
class CustomerSendMsg(OnlineBaseSaasApi):
    def send_request(self, **kwargs):
        self.data = {
            'uid': '${uid}',
            'cid': '${cid}',
            'partnerId': '${partnerId}',
            'sysNum': '${sysNum}',
            'agid': '${agid}',
            'message': '客服消息内容',
            # ... 其他参数
        }
        
        # 刷新所有相关变量
        self.refresh_data_with_extracted_params(
            'uid', 'cid', 'partnerId', 'sysNum', 'agid'
        )
        
        return super().send_request(**kwargs)
```

## 💡 **最佳实践**

### **1. 参数命名规范**
- 使用描述性的参数名
- 保持与API文档一致的命名
- 区分响应字段和请求参数

### **2. 变量管理**
- 统一使用变量管理器存储提取的参数
- 在接口间共享变量管理器实例
- 及时刷新变量以确保数据一致性

### **3. 错误处理**
- 检查参数是否成功提取
- 处理参数缺失的情况
- 提供有意义的错误信息

### **4. 性能优化**
- 只提取需要的参数
- 避免重复提取相同参数
- 合理使用缓存机制

## 🔍 **调试技巧**

### **1. 查看提取的参数**
```python
# 打印所有提取的参数
print("提取的参数:")
for param in ['uid', 'userId', 'partnerId', 'sysNum']:
    value = self.variables_manager.get_variable(param)
    print(f"  {param}: {value}")
```

### **2. 验证参数有效性**
```python
# 检查关键参数是否存在
required_params = ['uid', 'userId', 'partnerId']
missing_params = []

for param in required_params:
    value = self.variables_manager.get_variable(param)
    if not value:
        missing_params.append(param)

if missing_params:
    print(f"缺失参数: {missing_params}")
```

### **3. 参数追踪**
```python
# 追踪参数的来源和使用
print(f"参数来源: 用户初始化接口")
print(f"参数用途: {self.__class__.__name__} 接口")
print(f"参数值: partnerId={self.variables_manager.get_variable('partnerId')}")
```

## 📝 **总结**

通过参数提取机制，您可以：

1. **✅ 复用会话数据**：在多个接口间共享用户会话信息
2. **✅ 保持数据一致性**：确保所有接口使用相同的用户标识
3. **✅ 简化接口调用**：自动填充必要的参数
4. **✅ 提高开发效率**：减少手动参数配置工作
5. **✅ 增强可维护性**：集中管理参数提取逻辑

**参数提取功能让接口间的数据传递更加智能和自动化！** 🎉
