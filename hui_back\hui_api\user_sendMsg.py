"""
用户发送消息
"""
from ..hui_common.base_api import OnlineBaseSaasApi
from ..hui_common.time_utils import get_current_timestamp
from faker import Faker
fake = Faker(locale='zh_CN') # 生成中文文本



class User_sendMsg(OnlineBaseSaasApi):
    """用户发送消息API类"""

    def send_request(self, **kwargs):
        """通用方法实现：配置 + 刷新变量 + 发送请求"""
        # 配置API - 使用新的URL构建逻辑
        self.url = self.build_url('user_sendMsg_url', 'user_sendMsg_path')
        self.method, self.headers, self.json = 'post', {'content-type': 'application/x-www-form-urlencoded'}, None


        self.data = {
            'uid': '${uid}',
            'cid': '${cid}',
            'puid': '${puid}',
            'content': fake.sentence(),
            'objMsgType': '',
            'msgType': 0,
            'fileName': 'undefinded',
            'msgId': '', # 先留空，后面动态生成
            'resend':  ''
      


        }

        # 刷新变量并发送请求
        self.refresh_data_with_extracted_params('uid', 'cid','puid')

        # 获取真实uid并生成msgId
        uid = self.data.get('uid', '')
        msg_id = f"{uid}{get_current_timestamp()}"
        self.data['msgId'] = msg_id

        return super().send_request(**kwargs)

