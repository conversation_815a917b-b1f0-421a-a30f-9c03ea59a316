"""
用户服务总结-无效服务总结
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class ServicesSummary(OnlineBaseSaasApi):
    """无效服务总结API类"""

    def send_request(self, **kwargs):
        """通用方法实现：配置 + 刷新变量 + 发送请求"""
        # 获取配置并一次性完成所有设置
        config = self.configInfo

        # 配置API - 使用新的URL构建逻辑
        self.url = self.build_url('services_summary_url', 'services_summary_path')
        self.method, self.headers, self.json = 'post', {'content-type': 'application/x-www-form-urlencoded'}, None
        self.data = {
            'updateServiceId': config.get('Y', ''),
            'uid': '${uid}',
            'cid': '${cid}',
            'invalidSession': 1
        }

        # 刷新变量并发送请求
        self.refresh_data_with_extracted_params('uid', 'cid')
        return super().send_request(**kwargs)

