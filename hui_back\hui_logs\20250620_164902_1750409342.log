[2025-06-20 16:49:02,744] [INFO] [apitest] - #######################################################################
[2025-06-20 16:49:02,745] [INFO] [apitest] - 完整响应内容: {
  "status": 0,
  "message": "success",
  "data": {
    "cid": "796bc43f3e5245a8a04413161df96fdc",
    "uid": "56de2146104f480d8418d5400c978305",
    "puid": "32c4eb8a4ed94ec4af32a90c782a562b",
    "userId": null,
    "companyId": "bdbe5660b2eb45758739527fda974091"
  }
}
[2025-06-20 16:49:02,746] [INFO] [apitest] - 通过【$.data.userId】提取到的结果是:None
[2025-06-20 16:49:02,746] [INFO] [apitest] - #######################################################################


[2025-06-20 16:49:02,747] [INFO] [apitest] - 通过路径 $.data.userId 没有找到userId，尝试使用extract_userId方法搜索更多路径
[2025-06-20 16:49:02,748] [INFO] [apitest] - #######################################################################
[2025-06-20 16:49:02,749] [INFO] [apitest] - 尝试提取userId，响应内容: {"status": 0, "message": "success", "data": {"cid": "796bc43f3e5245a8a04413161df96fdc", "uid": "56de2146104f480d8418d5400c978305", "puid": "32c4eb8a4ed94ec4af32a90c782a562b", "userId": null, "companyI...
[2025-06-20 16:49:02,749] [INFO] [apitest] - 响应中顶级字段: ['status', 'message', 'data']
[2025-06-20 16:49:02,750] [INFO] [apitest] - 通过深度搜索找到的userId值: None
[2025-06-20 16:49:02,750] [INFO] [apitest] - 已将userId值 None 保存为变量 $userId
[2025-06-20 16:49:02,751] [INFO] [apitest] - #######################################################################


[2025-06-20 16:49:02,751] [INFO] [apitest] - 成功通过extract_userId方法找到值: None
[2025-06-20 16:49:02,752] [INFO] [apitest] - #######################################################################
[2025-06-20 16:49:02,753] [INFO] [apitest] - 尝试提取userId，响应内容: {"status": 0, "message": "success", "data": {"cid": "796bc43f3e5245a8a04413161df96fdc", "uid": "56de2146104f480d8418d5400c978305", "puid": "32c4eb8a4ed94ec4af32a90c782a562b", "userId": null, "companyI...
[2025-06-20 16:49:02,753] [INFO] [apitest] - 响应中顶级字段: ['status', 'message', 'data']
[2025-06-20 16:49:02,754] [WARNING] [apitest] - 无法从响应中提取userId，请检查响应格式或手动指定正确的JSONPath
[2025-06-20 16:49:02,755] [INFO] [apitest] - #######################################################################


[2025-06-20 16:49:02,756] [INFO] [apitest] - #######################################################################
[2025-06-20 16:49:02,756] [INFO] [apitest] - 尝试提取userId，响应内容: {"status": 0, "message": "success", "data": {"cid": "796bc43f3e5245a8a04413161df96fdc", "uid": "56de2146104f480d8418d5400c978305", "puid": "32c4eb8a4ed94ec4af32a90c782a562b", "userId": null, "companyI...
[2025-06-20 16:49:02,757] [INFO] [apitest] - 响应中顶级字段: ['status', 'message', 'data']
[2025-06-20 16:49:02,757] [INFO] [apitest] - 通过深度搜索找到的userId值: None
[2025-06-20 16:49:02,758] [INFO] [apitest] - 已将userId值 None 保存为变量 $userId
[2025-06-20 16:49:02,758] [INFO] [apitest] - #######################################################################


