"""
在线工作台-标星
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class Stars(OnlineBaseSaasApi):
    """标星API类 """

    def send_request(self, **kwargs):
        """通用方法实现：配置 + 刷新变量 + 发送请求"""
        # 配置API - 使用新的URL构建逻辑
        self.url = self.build_url('stars_url', 'stars_path')
        self.method, self.headers, self.json = 'post', {'content-type': 'application/x-www-form-urlencoded'}, None
        self.data = {
            'sender': '${uid}',    # 使用变量引用，自动替换
            'receiver': '${cid}'   # 使用变量引用，自动替换
        }

        # 刷新变量并发送请求
        self.refresh_data_with_extracted_params('uid', 'cid')
        return super().send_request(**kwargs)
