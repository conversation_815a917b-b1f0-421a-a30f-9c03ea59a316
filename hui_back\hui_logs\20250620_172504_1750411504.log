[2025-06-20 17:25:04,763] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,764] [INFO] [apitest] - 完整响应内容: {
  "status": "success",
  "code": 200,
  "data": {
    "user": {
      "id": "real_user_12345",
      "name": "张三",
      "email": "<EMAIL>",
      "profile": {
        "userId": "profile_user_67890",
        "avatar": "https://example.com/avatar.jpg",
        "settings": {
          "theme": "dark",
          "language": "zh-CN",
          "notifications": {
            "email": true,
            "sms": false
          }
        }
      }
    },
    "session": {
      "sessionId": "sess_abc123",
      "token": "jwt_token_xyz789",
      "expires": 3600
    },
    "permissions": [
      {
        "name": "read",
        "scope": "user"
      },
      {
        "name": "write",
        "scope": "profile"
      },
      {
        "name": "admin",
        "scope": "system"
      }
    ]
  },
  "metadata": {
    "timestamp": 1640995200,
    "version": "v2.1.0",
    "server": "api-server-01",
    "config": {
      "features": {
        "advanced_search": true,
        "real_tim
[2025-06-20 17:25:04,770] [INFO] [apitest] - 通过【$.userId】提取到的结果是:None
[2025-06-20 17:25:04,771] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,771] [INFO] [apitest] - 通过路径 $.userId 没有找到userId，尝试使用extract_userId方法搜索更多路径
[2025-06-20 17:25:04,773] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,774] [INFO] [apitest] - 尝试提取userId，响应内容: {"status": "success", "code": 200, "data": {"user": {"id": "real_user_12345", "name": "张三", "email": "<EMAIL>", "profile": {"userId": "profile_user_67890", "avatar": "https://example.com/...
[2025-06-20 17:25:04,775] [INFO] [apitest] - 响应中顶级字段: ['status', 'code', 'data', 'metadata', 'uid', 'cid', 'puid', 'userId', 'companyId']
[2025-06-20 17:25:04,777] [INFO] [apitest] - 通过深度搜索找到的userId值: profile_user_67890
[2025-06-20 17:25:04,778] [INFO] [apitest] - 已将userId值 profile_user_67890 保存为变量 $userId
[2025-06-20 17:25:04,779] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,779] [INFO] [apitest] - 成功通过extract_userId方法找到值: profile_user_67890
[2025-06-20 17:25:04,781] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,782] [INFO] [apitest] - 使用JSONPath提取器: $.status -> 变量: api_status
[2025-06-20 17:25:04,787] [INFO] [apitest] - JSONPath提取成功: $.status = success
[2025-06-20 17:25:04,789] [INFO] [apitest] - 已将提取值保存为变量 $api_status
[2025-06-20 17:25:04,790] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,790] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,792] [INFO] [apitest] - 使用JSONPath提取器: $.code -> 变量: response_code
[2025-06-20 17:25:04,793] [INFO] [apitest] - JSONPath提取成功: $.code = 200
[2025-06-20 17:25:04,793] [INFO] [apitest] - 已将提取值保存为变量 $response_code
[2025-06-20 17:25:04,794] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,795] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,795] [INFO] [apitest] - 使用JSONPath提取器: $.data.user.name -> 变量: user_name
[2025-06-20 17:25:04,796] [INFO] [apitest] - JSONPath提取成功: $.data.user.name = 张三
[2025-06-20 17:25:04,797] [INFO] [apitest] - 已将提取值保存为变量 $user_name
[2025-06-20 17:25:04,797] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,798] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,798] [INFO] [apitest] - 使用JSONPath提取器: $.data.user.email -> 变量: user_email
[2025-06-20 17:25:04,799] [INFO] [apitest] - JSONPath提取成功: $.data.user.email = <EMAIL>
[2025-06-20 17:25:04,799] [INFO] [apitest] - 已将提取值保存为变量 $user_email
[2025-06-20 17:25:04,800] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,800] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,802] [INFO] [apitest] - 使用JSONPath提取器: $.data.session.token -> 变量: session_token
[2025-06-20 17:25:04,805] [INFO] [apitest] - JSONPath提取成功: $.data.session.token = jwt_token_xyz789
[2025-06-20 17:25:04,806] [INFO] [apitest] - 已将提取值保存为变量 $session_token
[2025-06-20 17:25:04,807] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,808] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,811] [INFO] [apitest] - 使用JSONPath提取器: $.data.user.profile.settings.theme -> 变量: theme
[2025-06-20 17:25:04,812] [INFO] [apitest] - JSONPath提取成功: $.data.user.profile.settings.theme = dark
[2025-06-20 17:25:04,813] [INFO] [apitest] - 已将提取值保存为变量 $theme
[2025-06-20 17:25:04,816] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,817] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,818] [INFO] [apitest] - 使用JSONPath提取器: $.data.user.profile.settings.language -> 变量: language
[2025-06-20 17:25:04,823] [INFO] [apitest] - JSONPath提取成功: $.data.user.profile.settings.language = zh-CN
[2025-06-20 17:25:04,826] [INFO] [apitest] - 已将提取值保存为变量 $language
[2025-06-20 17:25:04,827] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,828] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,829] [INFO] [apitest] - 使用JSONPath提取器: $.data.user.profile.settings.notifications.email -> 变量: email_notify
[2025-06-20 17:25:04,831] [INFO] [apitest] - JSONPath提取成功: $.data.user.profile.settings.notifications.email = True
[2025-06-20 17:25:04,832] [INFO] [apitest] - 已将提取值保存为变量 $email_notify
[2025-06-20 17:25:04,833] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,834] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,836] [INFO] [apitest] - 使用JSONPath提取器: $.data.permissions[0].name -> 变量: first_permission
[2025-06-20 17:25:04,837] [INFO] [apitest] - JSONPath提取成功: $.data.permissions[0].name = read
[2025-06-20 17:25:04,838] [INFO] [apitest] - 已将提取值保存为变量 $first_permission
[2025-06-20 17:25:04,838] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,839] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,840] [INFO] [apitest] - 使用JSONPath提取器: $.data.permissions[1].scope -> 变量: second_permission_scope
[2025-06-20 17:25:04,841] [INFO] [apitest] - JSONPath提取成功: $.data.permissions[1].scope = profile
[2025-06-20 17:25:04,842] [INFO] [apitest] - 已将提取值保存为变量 $second_permission_scope
[2025-06-20 17:25:04,843] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,844] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,845] [INFO] [apitest] - 使用JSONPath提取器: $.data.permissions[2].name -> 变量: admin_permission
[2025-06-20 17:25:04,845] [INFO] [apitest] - JSONPath提取成功: $.data.permissions[2].name = admin
[2025-06-20 17:25:04,847] [INFO] [apitest] - 已将提取值保存为变量 $admin_permission
[2025-06-20 17:25:04,848] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,850] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,852] [INFO] [apitest] - 使用JSONPath提取器: $.metadata.version -> 变量: version
[2025-06-20 17:25:04,853] [INFO] [apitest] - JSONPath提取成功: $.metadata.version = v2.1.0
[2025-06-20 17:25:04,854] [INFO] [apitest] - 已将提取值保存为变量 $version
[2025-06-20 17:25:04,854] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,855] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,855] [INFO] [apitest] - 使用JSONPath提取器: $.metadata.server -> 变量: server
[2025-06-20 17:25:04,856] [INFO] [apitest] - JSONPath提取成功: $.metadata.server = api-server-01
[2025-06-20 17:25:04,857] [INFO] [apitest] - 已将提取值保存为变量 $server
[2025-06-20 17:25:04,857] [INFO] [apitest] - #######################################################################


[2025-06-20 17:25:04,859] [INFO] [apitest] - #######################################################################
[2025-06-20 17:25:04,860] [INFO] [apitest] - 使用JSONPath提取器: $.metadata.config.features.advanced_search -> 变量: advanced_search
[2025-06-20 17:25:04,861] [INFO] [apitest] - JSONPath提取成功: $.metadata.config.features.advanced_search = True
[2025-06-20 17:25:04,861] [INFO] [apitest] - 已将提取值保存为变量 $advanced_search
[2025-06-20 17:25:04,862] [INFO] [apitest] - #######################################################################


