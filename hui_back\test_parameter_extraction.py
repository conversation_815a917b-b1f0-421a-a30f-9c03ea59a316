#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试参数提取功能
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_parameter_extraction():
    """测试参数提取功能"""
    
    print("=" * 80)
    print("参数提取功能测试")
    print("=" * 80)
    
    try:
        # 1. 测试用户初始化的参数提取
        print("\n1. 测试用户初始化参数提取...")
        from hui_api.user_init import UserInit
        
        user_init = UserInit()
        init_response = user_init.send_request()
        
        if not init_response or not hasattr(init_response, 'resp') or init_response.resp.status_code != 200:
            print("❌ 用户初始化失败")
            return False
        
        print("✅ 用户初始化成功")
        
        # 2. 验证响应字段提取
        print("\n2. 验证响应字段提取...")
        response_fields = {
            'uid': user_init.uid,
            'userId': user_init.userId,
            'cid': user_init.cid,
            'puid': user_init.puid,
            'companyId': getattr(user_init, 'companyId', None),
            'schemeId': getattr(user_init, 'schemeId', None)
        }
        
        print("响应字段提取结果:")
        for field, value in response_fields.items():
            status = "✅" if value else "❌"
            print(f"  {status} {field}: {value}")
        
        # 3. 验证请求参数提取
        print("\n3. 验证请求参数提取...")
        request_params = {
            'partnerId': getattr(user_init, 'partnerId', None),
            'sysNum': getattr(user_init, 'sysNum', None),
            'agid': getattr(user_init, 'agid', None),
            'xst': getattr(user_init, 'xst', None)
        }
        
        print("请求参数提取结果:")
        for param, value in request_params.items():
            status = "✅" if value is not None else "❌"
            print(f"  {status} {param}: {value}")
        
        # 4. 验证变量管理器中的参数
        print("\n4. 验证变量管理器中的参数...")
        all_params = ['uid', 'userId', 'cid', 'puid', 'partnerId', 'sysNum', 'agid', 'xst']
        
        print("变量管理器中的参数:")
        for param in all_params:
            value = user_init.variables_manager.get_variable(param)
            status = "✅" if value else "❌"
            print(f"  {status} {param}: {value}")
        
        # 5. 测试在其他接口中使用提取的参数
        print("\n5. 测试在保存用户信息接口中使用提取的参数...")
        from hui_api.save_userInfo import Save_userInfo
        
        save_userinfo = Save_userInfo()
        save_userinfo.variables_manager = user_init.variables_manager
        save_userinfo.session = user_init.session
        
        # 检查请求数据中的参数替换
        print("保存用户信息接口的请求参数:")
        key_params = ['uid', 'userId', 'partnerId', 'cid']
        for param in key_params:
            value = save_userinfo.data.get(param, 'NOT_SET')
            print(f"  {param}: {value}")
        
        # 发送请求测试
        save_response = save_userinfo.send_request()
        
        if save_response and hasattr(save_response, 'resp') and save_response.resp.status_code == 200:
            print("✅ 保存用户信息接口调用成功")
            
            # 检查实际发送的参数
            print("\n实际发送的参数值:")
            for param in key_params:
                actual_value = save_userinfo.data.get(param, 'NOT_SET')
                print(f"  {param}: {actual_value}")
                
        else:
            print("❌ 保存用户信息接口调用失败")
        
        # 6. 总结测试结果
        print("\n6. 测试结果总结...")
        
        # 统计成功提取的参数数量
        successful_response_fields = sum(1 for v in response_fields.values() if v)
        successful_request_params = sum(1 for v in request_params.values() if v is not None)
        
        print(f"响应字段提取成功: {successful_response_fields}/{len(response_fields)}")
        print(f"请求参数提取成功: {successful_request_params}/{len(request_params)}")
        
        # 检查关键参数
        critical_params = ['uid', 'userId', 'cid', 'partnerId']
        critical_success = 0
        for param in critical_params:
            value = user_init.variables_manager.get_variable(param)
            if value:
                critical_success += 1
        
        print(f"关键参数提取成功: {critical_success}/{len(critical_params)}")
        
        if critical_success == len(critical_params):
            print("✅ 参数提取功能测试通过")
            return True
        else:
            print("❌ 关键参数提取不完整")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def demonstrate_usage_scenarios():
    """演示不同的使用场景"""
    
    print("\n" + "=" * 80)
    print("参数提取使用场景演示")
    print("=" * 80)
    
    print("\n场景1: 基础会话参数复用")
    print("- 用途: 在多个接口间共享用户会话信息")
    print("- 参数: uid, userId, cid, puid")
    print("- 示例: 用户发送消息、客服回复、服务评价等")
    
    print("\n场景2: 业务标识参数复用")
    print("- 用途: 保持业务流程的一致性")
    print("- 参数: partnerId, sysNum, agid")
    print("- 示例: 订单处理、支付流程、数据统计等")
    
    print("\n场景3: 配置参数复用")
    print("- 用途: 保持配置的一致性")
    print("- 参数: companyId, schemeId, visitSchemeId")
    print("- 示例: 界面配置、功能开关、权限控制等")
    
    print("\n场景4: 技术参数复用")
    print("- 用途: 保持技术实现的一致性")
    print("- 参数: source, language, ack, newFlag")
    print("- 示例: 多语言支持、渠道统计、版本控制等")

if __name__ == "__main__":
    print("开始测试参数提取功能...")
    
    success = test_parameter_extraction()
    
    demonstrate_usage_scenarios()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 参数提取功能测试成功！")
        print("现在您可以在任何接口中使用从用户初始化提取的参数")
    else:
        print("💥 参数提取功能测试失败")
        print("请检查用户初始化接口和参数提取逻辑")
    print("=" * 80)
    
    sys.exit(0 if success else 1)
