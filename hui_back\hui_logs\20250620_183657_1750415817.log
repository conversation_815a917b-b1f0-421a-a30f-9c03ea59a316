[2025-06-20 18:36:57,307] [INFO] [apitest] - ===== 开始执行自动化测试 =====
[2025-06-20 18:36:57,314] [INFO] [apitest] - 指定环境: ali
[2025-06-20 18:36:57,315] [INFO] [root] - 设置登录配置: user=<EMAIL>
[2025-06-20 18:36:57,315] [INFO] [root] - 【登录操作】尝试登录到 ali 环境
[2025-06-20 18:36:57,315] [INFO] [root] - 【环境配置】开始加载 ali 环境配置
[2025-06-20 18:36:57,315] [DEBUG] [root] - 配置文件路径: C:\Users\<USER>\Downloads\API_Online-master-26 - 副本\hui_back\hui_config\hui_online_common.yml
[2025-06-20 18:36:57,316] [DEBUG] [root] - 读取YAML文件: C:\Users\<USER>\Downloads\API_Online-master-26 - 副本\hui_back\hui_config\hui_online_common.yml
[2025-06-20 18:36:57,320] [INFO] [root] - YAML配置中的环境节点: ali
[2025-06-20 18:36:57,321] [INFO] [root] - 成功加载 ali 环境配置
[2025-06-20 18:36:57,321] [INFO] [root] - 【环境节点】ali 环境信息:
[2025-06-20 18:36:57,321] [INFO] [root] -   - URL: http://api-c.sobot.com
[2025-06-20 18:36:57,322] [INFO] [root] -   - 路径: /basic-login/account/consoleLogin/4
[2025-06-20 18:36:57,322] [INFO] [root] -   - 登录用户: <EMAIL>
[2025-06-20 18:36:57,323] [DEBUG] [hui_back.hui_common.v6_console_login] - Current headers before update: {}
[2025-06-20 18:36:57,323] [INFO] [root] - 设置 content-type: application/json (域名: http://api-c.sobot.com)
[2025-06-20 18:36:57,323] [DEBUG] [hui_back.hui_common.v6_console_login] - Headers after update: {'content-type': 'application/json'}
[2025-06-20 18:36:57,323] [DEBUG] [root] - 登录参数: user=<EMAIL>, flag=1, terminalCode=2
[2025-06-20 18:36:57,324] [DEBUG] [root] - 额外参数: {}
[2025-06-20 18:36:57,324] [DEBUG] [root] - 请求数据格式: JSON
[2025-06-20 18:36:57,324] [INFO] [root] - 【API请求】发送登录请求到: http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-20 18:36:57,324] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################
[2025-06-20 18:36:57,325] [INFO] [hui_back.hui_common.v6_console_login] - 请求方法：post
[2025-06-20 18:36:57,325] [INFO] [hui_back.hui_common.v6_console_login] - 请求地址：http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-20 18:36:57,325] [INFO] [hui_back.hui_common.v6_console_login] - 请求参数 json：{'loginUser': '<EMAIL>', 'loginPwd': 'bGl1eWgxMjM=', 'loginFlag': 1, 'terminalCode': 2}
[2025-06-20 18:36:57,330] [DEBUG] [urllib3.connectionpool] - Starting new HTTP connection (1): 127.0.0.1:9999
