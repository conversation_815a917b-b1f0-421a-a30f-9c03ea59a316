#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例接口：展示如何使用从用户初始化中提取的参数
"""
from ..hui_common.base_api import OnlineBaseSaasApi
from faker import Faker
fake = Faker(locale='zh_CN')

class ExampleUsingExtractedParams(OnlineBaseSaasApi):
    """示例API类：展示如何使用提取的参数"""

    def send_request(self, **kwargs):
        """演示如何使用从用户初始化中提取的各种参数"""
        
        # 配置API
        config = self.configInfo
        self.url = self.build_url('example_url', 'example_path')
        self.method = 'post'
        self.headers = {'content-type': 'application/x-www-form-urlencoded'}
        self.json = None
        
        # 构建请求数据，使用从用户初始化中提取的参数
        self.data = {
            # 基础会话参数（从响应中提取）
            'uid': '${uid}',                    # 用户ID
            'userId': '${userId}',              # 用户标识
            'cid': '${cid}',                    # 客服ID
            'puid': '${puid}',                  # 父用户ID
            'companyId': '${companyId}',        # 公司ID
            'schemeId': '${schemeId}',          # 方案ID
            'visitSchemeId': '${visitSchemeId}', # 访问方案ID
            
            # 请求参数（从请求中提取）
            'partnerId': '${partnerId}',        # 合作伙伴ID
            'sysNum': '${sysNum}',              # 系统编号
            'agid': '${agid}',                  # 代理ID
            'xst': '${xst}',                    # 扩展标识
            'source': '${source}',              # 来源
            'language': '${language}',          # 语言
            'ack': '${ack}',                    # 确认标识
            'isReComment': '${isReComment}',    # 是否重新评论
            'newFlag': '${newFlag}',            # 新标识
            'isJs': '${isJs}',                  # 是否JS
            
            # 业务参数
            'action': 'example_action',
            'timestamp': fake.random_int(1000000000, 9999999999),
            'content': fake.sentence(),
            'remark': '使用提取参数的示例接口'
        }
        
        # 刷新所有变量
        self.refresh_data_with_extracted_params(
            # 响应提取的参数
            'uid', 'userId', 'cid', 'puid', 'companyId', 'schemeId', 'visitSchemeId',
            # 请求提取的参数
            'partnerId', 'sysNum', 'agid', 'xst', 'source', 'language', 
            'ack', 'isReComment', 'newFlag', 'isJs'
        )
        
        # 打印使用的参数信息
        print("=" * 60)
        print("示例接口使用的提取参数:")
        print("=" * 60)
        
        print("响应提取的参数:")
        response_params = ['uid', 'userId', 'cid', 'puid', 'companyId', 'schemeId', 'visitSchemeId']
        for param in response_params:
            value = self.variables_manager.get_variable(param)
            print(f"  {param}: {value}")
        
        print("\n请求提取的参数:")
        request_params = ['partnerId', 'sysNum', 'agid', 'xst', 'source', 'language', 'ack', 'isReComment', 'newFlag', 'isJs']
        for param in request_params:
            value = self.variables_manager.get_variable(param)
            print(f"  {param}: {value}")
        
        print("\n最终请求数据中的关键参数:")
        key_params = ['uid', 'userId', 'partnerId', 'sysNum', 'agid']
        for param in key_params:
            value = self.data.get(param, 'NOT_SET')
            print(f"  {param}: {value}")
        
        print("=" * 60)
        
        # 发送请求
        response = super().send_request(**kwargs)
        
        return response

# 使用示例函数
def demonstrate_parameter_extraction():
    """演示参数提取的完整流程"""
    
    print("参数提取演示")
    print("=" * 80)
    
    # 1. 用户初始化（提取参数）
    print("1. 执行用户初始化，提取参数...")
    from .user_init import UserInit
    user_init = UserInit()
    init_response = user_init.send_request()
    
    if init_response:
        print("✅ 用户初始化成功，参数已提取")
    else:
        print("❌ 用户初始化失败")
        return
    
    # 2. 使用提取的参数
    print("\n2. 使用提取的参数调用示例接口...")
    example_api = ExampleUsingExtractedParams()
    
    # 共享变量管理器和会话
    example_api.variables_manager = user_init.variables_manager
    example_api.session = user_init.session
    
    # 调用示例接口
    example_response = example_api.send_request()
    
    if example_response:
        print("✅ 示例接口调用成功")
    else:
        print("❌ 示例接口调用失败")
    
    print("\n" + "=" * 80)
    print("参数提取演示完成")

if __name__ == "__main__":
    demonstrate_parameter_extraction()
