[2025-06-20 17:22:15,678] [INFO] [apitest] - #######################################################################
[2025-06-20 17:22:15,679] [INFO] [apitest] - 完整响应内容: {
  "status": 0,
  "message": "success",
  "data": {
    "user": {
      "id": "user_12345",
      "name": "测试用户",
      "profile": {
        "userId": "profile_67890"
      }
    },
    "session": {
      "sessionId": "session_abc123"
    }
  },
  "uid": "56de2146104f480d8418d5400c978305",
  "cid": "796bc43f3e5245a8a04413161df96fdc",
  "puid": "32c4eb8a4ed94ec4af32a90c782a562b",
  "userId": null,
  "companyId": "bdbe5660b2eb45758739527fda974091",
  "metadata": {
    "items": [
      {
        "name": "item1",
        "value": "value1"
      },
      {
        "name": "item2",
        "value": "value2"
      }
    ],
    "config": {
      "settings": {
        "theme": "dark",
        "language": "zh-CN"
      }
    }
  }
}
[2025-06-20 17:22:15,680] [INFO] [apitest] - 通过【$.userId】提取到的结果是:None
[2025-06-20 17:22:15,681] [INFO] [apitest] - #######################################################################


[2025-06-20 17:22:15,682] [INFO] [apitest] - 通过路径 $.userId 没有找到userId，尝试使用extract_userId方法搜索更多路径
[2025-06-20 17:22:15,682] [INFO] [apitest] - #######################################################################
[2025-06-20 17:22:15,683] [INFO] [apitest] - 尝试提取userId，响应内容: {"status": 0, "message": "success", "data": {"user": {"id": "user_12345", "name": "测试用户", "profile": {"userId": "profile_67890"}}, "session": {"sessionId": "session_abc123"}}, "uid": "56de2146104f480d...
[2025-06-20 17:22:15,683] [INFO] [apitest] - 响应中顶级字段: ['status', 'message', 'data', 'uid', 'cid', 'puid', 'userId', 'companyId', 'metadata']
[2025-06-20 17:22:15,684] [INFO] [apitest] - 通过深度搜索找到的userId值: profile_67890
[2025-06-20 17:22:15,684] [INFO] [apitest] - 已将userId值 profile_67890 保存为变量 $userId
[2025-06-20 17:22:15,685] [INFO] [apitest] - #######################################################################


[2025-06-20 17:22:15,685] [INFO] [apitest] - 成功通过extract_userId方法找到值: profile_67890
[2025-06-20 17:22:15,687] [INFO] [apitest] - #######################################################################
[2025-06-20 17:22:15,687] [INFO] [apitest] - 使用JSONPath提取器: $.data.user.name -> 变量: user_name
[2025-06-20 17:22:15,688] [INFO] [apitest] - JSONPath提取成功: $.data.user.name = 测试用户
[2025-06-20 17:22:15,688] [INFO] [apitest] - 已将提取值保存为变量 $user_name
[2025-06-20 17:22:15,689] [INFO] [apitest] - #######################################################################


[2025-06-20 17:22:15,689] [INFO] [apitest] - #######################################################################
[2025-06-20 17:22:15,690] [INFO] [apitest] - 使用JSONPath提取器: $.data.session.sessionId -> 变量: session_id
[2025-06-20 17:22:15,690] [INFO] [apitest] - JSONPath提取成功: $.data.session.sessionId = session_abc123
[2025-06-20 17:22:15,690] [INFO] [apitest] - 已将提取值保存为变量 $session_id
[2025-06-20 17:22:15,691] [INFO] [apitest] - #######################################################################


[2025-06-20 17:22:15,691] [INFO] [apitest] - #######################################################################
[2025-06-20 17:22:15,692] [INFO] [apitest] - 使用JSONPath提取器: $.metadata.config.settings.theme -> 变量: theme
[2025-06-20 17:22:15,692] [INFO] [apitest] - JSONPath提取成功: $.metadata.config.settings.theme = dark
[2025-06-20 17:22:15,693] [INFO] [apitest] - 已将提取值保存为变量 $theme
[2025-06-20 17:22:15,693] [INFO] [apitest] - #######################################################################


[2025-06-20 17:22:15,694] [INFO] [apitest] - #######################################################################
[2025-06-20 17:22:15,695] [INFO] [apitest] - 使用JSONPath提取器: $.metadata.items[0].name -> 变量: first_item_name
[2025-06-20 17:22:15,696] [INFO] [apitest] - JSONPath提取成功: $.metadata.items[0].name = item1
[2025-06-20 17:22:15,696] [INFO] [apitest] - 已将提取值保存为变量 $first_item_name
[2025-06-20 17:22:15,697] [INFO] [apitest] - #######################################################################


[2025-06-20 17:22:15,697] [INFO] [apitest] - #######################################################################
[2025-06-20 17:22:15,698] [INFO] [apitest] - 使用JSONPath提取器: $.metadata.items[1].value -> 变量: second_item_value
[2025-06-20 17:22:15,699] [INFO] [apitest] - JSONPath提取成功: $.metadata.items[1].value = value2
[2025-06-20 17:22:15,699] [INFO] [apitest] - 已将提取值保存为变量 $second_item_value
[2025-06-20 17:22:15,700] [INFO] [apitest] - #######################################################################


[2025-06-20 17:22:15,701] [INFO] [apitest] - #######################################################################
[2025-06-20 17:22:15,703] [INFO] [apitest] - 使用JSONPath提取器: $.metadata.config.settings.language -> 变量: language
[2025-06-20 17:22:15,704] [INFO] [apitest] - JSONPath提取成功: $.metadata.config.settings.language = zh-CN
[2025-06-20 17:22:15,704] [INFO] [apitest] - 已将提取值保存为变量 $language
[2025-06-20 17:22:15,705] [INFO] [apitest] - #######################################################################


