[2025-06-20 17:43:16,280] [INFO] [apitest] - 【测试环境】开始设置测试环境
[2025-06-20 17:43:16,280] [INFO] [apitest] - 【环境选择】使用环境: ali
[2025-06-20 17:43:16,280] [INFO] [apitest] - 【登录流程】开始登录到 ali 环境
[2025-06-20 17:43:16,280] [INFO] [root] - 【快捷登录】使用快捷函数登录到 ali 环境
[2025-06-20 17:43:16,280] [INFO] [root] - 【登录操作】尝试登录到 ali 环境
[2025-06-20 17:43:16,280] [INFO] [root] - 【环境配置】开始加载 ali 环境配置
[2025-06-20 17:43:16,280] [DEBUG] [root] - 配置文件路径: C:\Users\<USER>\Downloads\API_Online-master-26 - 副本\hui_back\hui_config\hui_online_common.yml
[2025-06-20 17:43:16,282] [DEBUG] [root] - 读取YAML文件: C:\Users\<USER>\Downloads\API_Online-master-26 - 副本\hui_back\hui_config\hui_online_common.yml
[2025-06-20 17:43:16,286] [INFO] [root] - YAML配置中的环境节点: ali
[2025-06-20 17:43:16,287] [INFO] [root] - 成功加载 ali 环境配置
[2025-06-20 17:43:16,287] [INFO] [root] - 【环境节点】ali 环境信息:
[2025-06-20 17:43:16,287] [INFO] [root] -   - URL: http://api-c.sobot.com
[2025-06-20 17:43:16,287] [INFO] [root] -   - 路径: /basic-login/account/consoleLogin/4
[2025-06-20 17:43:16,287] [INFO] [root] -   - 登录用户: <EMAIL>
[2025-06-20 17:43:16,287] [DEBUG] [hui_back.hui_common.v6_console_login] - Current headers before update: {}
[2025-06-20 17:43:16,287] [INFO] [root] - 设置 content-type: application/json (域名: http://api-c.sobot.com)
[2025-06-20 17:43:16,287] [DEBUG] [hui_back.hui_common.v6_console_login] - Headers after update: {'content-type': 'application/json'}
[2025-06-20 17:43:16,287] [DEBUG] [root] - 登录参数: user=<EMAIL>, flag=1, terminalCode=2
[2025-06-20 17:43:16,287] [DEBUG] [root] - 额外参数: {}
[2025-06-20 17:43:16,288] [DEBUG] [root] - 请求数据格式: JSON
[2025-06-20 17:43:16,288] [INFO] [root] - 【API请求】发送登录请求到: http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-20 17:43:16,288] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################
[2025-06-20 17:43:16,288] [INFO] [hui_back.hui_common.v6_console_login] - 请求方法：post
[2025-06-20 17:43:16,288] [INFO] [hui_back.hui_common.v6_console_login] - 请求地址：http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-20 17:43:16,288] [INFO] [hui_back.hui_common.v6_console_login] - 请求参数 json：{'loginUser': '<EMAIL>', 'loginPwd': 'bGl1eWgxMjM=', 'loginFlag': 1, 'terminalCode': 2}
[2025-06-20 17:43:16,289] [DEBUG] [urllib3.connectionpool] - Starting new HTTP connection (1): api-c.sobot.com:80
[2025-06-20 17:43:16,852] [DEBUG] [urllib3.connectionpool] - http://api-c.sobot.com:80 "POST /text/basic-login/account/consoleLogin/4 HTTP/1.1" 200 None
[2025-06-20 17:43:16,853] [INFO] [hui_back.hui_common.v6_console_login] - 响应码：200
[2025-06-20 17:43:16,853] [INFO] [hui_back.hui_common.v6_console_login] - 响应体：{"companyId":"bdbe5660b2eb45758739527fda974091","item":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************.w-CfY99_7A1LFYU8NWmhr6NEbxc7EoXoFIx-q1G8vJg","items":[],"loginCategory":"console,serviceConsole,appConsole","newConsoleFlag":1,"retCode":"000000","retMsg":"操作成功","zone":1}
[2025-06-20 17:43:16,853] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################


[2025-06-20 17:43:16,853] [INFO] [root] - 【API响应】请求完成，耗时: 0.57秒，状态码: 200
[2025-06-20 17:43:16,853] [INFO] [root] - 【响应验证】验证成功，返回码: 000000
[2025-06-20 17:43:16,853] [DEBUG] [root] - 【响应JSON】: {"companyId": "bdbe5660b2eb45758739527fda974091", "item": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJj...
[2025-06-20 17:43:16,853] [DEBUG] [root] - 在 item 字符串中找到会话信息
[2025-06-20 17:43:16,854] [INFO] [root] - 【会话信息】成功提取会话信息: eyJ0eXAiOiJKV1Q...
[2025-06-20 17:43:16,854] [INFO] [root] - 【登录结果】登录成功，获取到会话信息
[2025-06-20 17:43:16,854] [INFO] [apitest] - 【登录成功】环境: ali, 会话ID: eyJ0eXAiOiJKV1Q...
[2025-06-20 17:43:16,854] [INFO] [apitest] - 【测试准备】环境设置完成，测试前登录成功
[2025-06-20 17:43:16,859] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:16,859] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:16,860] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-kwb/admin/online.action
[2025-06-20 17:43:16,860] [INFO] [apitest] - 请求参数 data：{'uid': 'default_1750412594_10c95d07', 'cid': 'default_1750412594_10c95d07'}
[2025-06-20 17:43:16,861] [INFO] [apitest] - 请求参数 json：{'uid': 'default_1750412594_10c95d07'}
[2025-06-20 17:43:16,863] [DEBUG] [urllib3.connectionpool] - Starting new HTTP connection (1): api-c.soboten.com:80
[2025-06-20 17:43:16,907] [DEBUG] [urllib3.connectionpool] - http://api-c.soboten.com:80 "POST /text/chat-kwb/admin/online.action HTTP/1.1" 200 0
[2025-06-20 17:43:16,907] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:16,908] [INFO] [apitest] - 响应体：
[2025-06-20 17:43:16,908] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:17,139] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:17,140] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:17,140] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-20 17:43:17,141] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=default_1750412594_10c95d07&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=default_1750412594_10c95d07
[2025-06-20 17:43:17,142] [DEBUG] [urllib3.connectionpool] - Starting new HTTP connection (1): api-c.soboten.com:80
[2025-06-20 17:43:17,525] [DEBUG] [urllib3.connectionpool] - http://api-c.soboten.com:80 "POST /text/chat-visit/user/init/v6 HTTP/1.1" 200 None
[2025-06-20 17:43:17,540] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:17,541] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":"7cff3c72501a4edabe4f344884af88a9","userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"db1e0b1ec0cd4ede8775ce7c79a6d0ff:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":1,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":0,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"e54029bf5c9c452eb948b94cfb3fefe2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"c229ae481f104c4cbfda50264b8ccd02","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-20 17:43:17,545] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:17,545] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:17,550] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:17,552] [INFO] [apitest] - 通过【$.cid】提取到的结果是:e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:17,553] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:17,553] [INFO] [apitest] - 将提取的值【e54029bf5c9c452eb948b94cfb3fefe2】保存为变量【cid】
[2025-06-20 17:43:17,554] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:17,556] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:17,557] [INFO] [apitest] - 通过【$.uid】提取到的结果是:56de2146104f480d8418d5400c978305
[2025-06-20 17:43:17,558] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:17,558] [INFO] [apitest] - 将提取的值【56de2146104f480d8418d5400c978305】保存为变量【uid】
[2025-06-20 17:43:17,559] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:17,562] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:17,563] [INFO] [apitest] - 通过【$.puid】提取到的结果是:c229ae481f104c4cbfda50264b8ccd02
[2025-06-20 17:43:17,563] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:17,564] [INFO] [apitest] - 将提取的值【c229ae481f104c4cbfda50264b8ccd02】保存为变量【puid】
[2025-06-20 17:43:17,564] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:17,566] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:17,567] [INFO] [apitest] - 通过【$.userId】提取到的结果是:7cff3c72501a4edabe4f344884af88a9
[2025-06-20 17:43:17,567] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:17,568] [INFO] [apitest] - 将提取的值【7cff3c72501a4edabe4f344884af88a9】保存为变量【userId】
[2025-06-20 17:43:17,569] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:17,570] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:17,570] [INFO] [apitest] - 请求地址：https://api-c.soboten.com/text/chat-kwb/message/send.action
[2025-06-20 17:43:17,571] [INFO] [apitest] - 请求参数 data：uid=56de2146104f480d8418d5400c978305&cid=e54029bf5c9c452eb948b94cfb3fefe2&tid=xOh8P5f7pLDPHW5kyWJscuzHfKg8v3Wj5TWF2jP6WSj%2BIBS1zqXJr8fZ4ny5vE8t&msgType=0&content=%E4%BA%BA%E5%91%98%E8%AE%BE%E5%A4%87%E7%B1%BB%E5%9E%8B%E7%BD%91%E4%B8%8A%E8%BF%99%E7%A7%8D.&objMsgType=&docId=&replyId=&fileName=&msgId=56de2146104f480d8418d5400c9783051750412597570&title=&answerId=&resend=
[2025-06-20 17:43:17,573] [DEBUG] [urllib3.connectionpool] - Starting new HTTPS connection (1): api-c.soboten.com:443
[2025-06-20 17:43:17,636] [DEBUG] [urllib3.connectionpool] - https://api-c.soboten.com:443 "POST /text/chat-kwb/message/send.action HTTP/1.1" 200 96
[2025-06-20 17:43:17,636] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:17,637] [INFO] [apitest] - 响应体：{"ustatus":99,"t":1750412599749,"retCode":"000000","retMsg":"成功","ts":"2025-06-20 17:43:19"}
[2025-06-20 17:43:17,637] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:17,657] [INFO] [hui_back.hui_testcases.test_login] - 成功获取会话ID: eyJ0eXAiOiJKV1Q...
[2025-06-20 17:43:17,659] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:17,660] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:17,660] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-20 17:43:17,661] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:17,662] [DEBUG] [urllib3.connectionpool] - Starting new HTTP connection (1): api-c.soboten.com:80
[2025-06-20 17:43:17,956] [DEBUG] [urllib3.connectionpool] - http://api-c.soboten.com:80 "POST /text/chat-visit/user/init/v6 HTTP/1.1" 200 None
[2025-06-20 17:43:17,968] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:17,969] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":"7cff3c72501a4edabe4f344884af88a9","userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"7f8ac64f82624b5e9ceb1eb50e910923:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":1,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":0,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"e54029bf5c9c452eb948b94cfb3fefe2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"c229ae481f104c4cbfda50264b8ccd02","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-20 17:43:17,971] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:17,972] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:17,974] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:17,975] [INFO] [apitest] - 通过【$.cid】提取到的结果是:e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:17,975] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:17,975] [INFO] [apitest] - 将提取的值【e54029bf5c9c452eb948b94cfb3fefe2】保存为变量【cid】
[2025-06-20 17:43:17,976] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:17,978] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:17,979] [INFO] [apitest] - 通过【$.uid】提取到的结果是:56de2146104f480d8418d5400c978305
[2025-06-20 17:43:17,980] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:17,980] [INFO] [apitest] - 将提取的值【56de2146104f480d8418d5400c978305】保存为变量【uid】
[2025-06-20 17:43:17,981] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:17,982] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:17,984] [INFO] [apitest] - 通过【$.puid】提取到的结果是:c229ae481f104c4cbfda50264b8ccd02
[2025-06-20 17:43:17,984] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:17,985] [INFO] [apitest] - 将提取的值【c229ae481f104c4cbfda50264b8ccd02】保存为变量【puid】
[2025-06-20 17:43:17,985] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:17,987] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:17,989] [INFO] [apitest] - 通过【$.userId】提取到的结果是:7cff3c72501a4edabe4f344884af88a9
[2025-06-20 17:43:17,989] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:17,990] [INFO] [apitest] - 将提取的值【7cff3c72501a4edabe4f344884af88a9】保存为变量【userId】
[2025-06-20 17:43:17,991] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:17,992] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:17,992] [INFO] [apitest] - 请求地址：https://api-c.soboten.com/text/chat-kwb/admin/addCustomerInfo.action
[2025-06-20 17:43:17,992] [INFO] [apitest] - 请求参数 data：uid=56de2146104f480d8418d5400c978305&customerFields=&sender=xOh8P5f7pLDPHW5kyWJscuzHfKg8v3Wj5TWF2jP6WSj%2BIBS1zqXJr8fZ4ny5vE8t&content=%E5%8A%9F%E8%83%BD%E9%98%85%E8%AF%BB%E4%B8%80%E7%82%B9.&userId=7cff3c72501a4edabe4f344884af88a9&tel=%5B%7B%22encrypt%22%3A%22***********%22%2C%22desensitization%22%3A%22***********%22%2C%22unDesensitization%22%3A%22***********%22%7D%5D&email=19000000000%40163.com&uname=%E5%8F%B6%E6%B6%9B&realname=%E8%80%BF%E6%B5%A9&qq=19000000000&remark=%E5%A4%87%E6%B3%A8&face=&weixin=&weibo=&sex=&age=&birthday=&partnerId=&createSource=&tid=xOh8P5f7pLDPHW5kyWJscuzHfKg8v3Wj5TWF2jP6WSj%2BIBS1zqXJr8fZ4ny5vE8t&proviceId=&proviceName=&cityId=&cityName=&areaId=&areaName=&enterpriseId=&enterpriseName=&countryId=&countryName=&isVip=&vipLevel=&userLabel=&cid=e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:17,994] [DEBUG] [urllib3.connectionpool] - Starting new HTTPS connection (1): api-c.soboten.com:443
[2025-06-20 17:43:18,098] [DEBUG] [urllib3.connectionpool] - https://api-c.soboten.com:443 "POST /text/chat-kwb/admin/addCustomerInfo.action HTTP/1.1" 200 None
[2025-06-20 17:43:18,101] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:18,102] [INFO] [apitest] - 响应体：{"item":{"userId":"d7b3a5893358406fb63907081ab5c949","id":"d7b3a5893358406fb63907081ab5c949","pid":"bdbe5660b2eb45758739527fda974091","type":0,"sysNum":null,"way":0,"purl":null,"status":8,"puid":null,"source":0,"createSource":0,"from":1,"partnerId":null,"uname":"昵称","tel":"[{\"desensitization\":\"***********\",\"encrypt\":\"***********\",\"unDesensitization\":\"***********\"}]","qq":"***********","email":"<EMAIL>","remark":"备注","visitTitle":null,"visitUrl":null,"face":"","nick":"昵称","weixin":"","weibo":null,"sex":"","age":null,"birthday":null,"params":null,"version":null,"realname":"姓名","os":null,"browser":null,"ip":null,"address":null,"lastCid":null,"lastAdminId":null,"isblack":0,"blackType":0,"ismark":0,"isTransfer":0,"isVisit":0,"lastTime":null,"groupId":"","groupName":"","appId":null,"agentId":null,"platFormId":null,"token":null,"rongyunId":null,"passwd":null,"t":null,"ts":null,"blackT":-1,"salt":null,"createTime":1750406061,"updateUserId":"83d4039186d8452b88a59873bfcf2084","adminMarkSet":"","lastMsg":"","lastMsgSenderType":null,"remoteStatus":0,"remoteSerialNum":0,"remoteSessionId":null,"createChatTime":null,"ack":false,"clearT":null,"terminal":null,"isInit":0,"equipmentId":null,"serialNum":null,"visitCount":null,"chooseAdminId":null,"isReComment":0,"debug":true,"pushable":true,"imConnStatus":0,"imWSAddress":null,"lastEndTime":null,"visitorIds":"6620af601b1448f1a560bfa217dd09fc","cardType":null,"cardNo":null,"enterpriseId":"","enterpriseName":"","proviceId":"","proviceName":"","cityId":"","cityName":"北京","areaId":"","areaName":"","countryName":null,"countryNameCopy":null,"countryId":null,"timezoneId":null,"timezoneValue":null,"language":null,"exFields":null,"isNew":1,"platformUnionCode":null,"platformUserId":null,"platformFlag":null,"phoneModel":null,"appVersion":null,"appName":null,"isVip":"0","vipLevel":"","multiParams":null,"flowType":0,"flowCompanyId":null,"companyName":null,"appointFlag":null,"queueType":0,"channelFlag":1,"userStatus":0,"privacyAgree":null,"beLock":2,"userLabel":"","summaryParams":null,"locale":null,"wxkfCompanyName":null,"thirdAdminId":null,"taskRobotId":null,"taskId":null,"taskName":null,"isJs":"-1","business":0,"validInformationFlag":0,"infomationSafety":0,"externalUserId":null,"paramUnionId":null,"eventKey":null,"eventKeyFlag":null,"receptionVersion":0,"ruleId":null,"translateLanguage":null,"leaveId":null,"leaveMsgId":null,"leaveChatTime":null,"whatsappIds":[],"faceBookIds":[],"instagramIds":[],"lineIds":[],"telegramIds":[],"discordIds":[],"wxUnionIds":[],"wxOpenIds":[],"wxExternalUserIds":[],"twitterIds":null,"partnerIds":[],"mergedUserIds":null,"whatsappSendFlag":null,"ruleWapSendFlag":null,"liableServiceId":null,"liableServiceName":null,"isUnreadMarkingEnabled":null,"shopifyDomain":null,"shopifyShopId":null,"tikTokUser":null,"browserLan":null,"senderEmail":null,"receiveEmail":null,"senderName":null,"receiveName":null,"channelCenterBiz":null,"unionId":null,"resultList":[],"blackMan":0,"blackTime":"","wxMiniPartnerId":null,"wxkfpartnerId":null,"ackRequired":false},"size":1,"confictColumn":"tel","confictColumnValue":"[{\"desensitization\":\"***********\",\"encrypt\":\"***********\",\"unDesensitization\":\"***********\"}]","status":1}
[2025-06-20 17:43:18,103] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:18,117] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:18,118] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:18,118] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-20 17:43:18,119] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:18,121] [DEBUG] [urllib3.connectionpool] - Starting new HTTP connection (1): api-c.soboten.com:80
[2025-06-20 17:43:18,548] [DEBUG] [urllib3.connectionpool] - http://api-c.soboten.com:80 "POST /text/chat-visit/user/init/v6 HTTP/1.1" 200 None
[2025-06-20 17:43:18,572] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:18,572] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":"7cff3c72501a4edabe4f344884af88a9","userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"7398bca60859444bace6f3ab4b6db393:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":1,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":0,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"e54029bf5c9c452eb948b94cfb3fefe2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"c229ae481f104c4cbfda50264b8ccd02","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-20 17:43:18,574] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:18,574] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:18,576] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:18,576] [INFO] [apitest] - 通过【$.cid】提取到的结果是:e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:18,577] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:18,577] [INFO] [apitest] - 将提取的值【e54029bf5c9c452eb948b94cfb3fefe2】保存为变量【cid】
[2025-06-20 17:43:18,578] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:18,579] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:18,580] [INFO] [apitest] - 通过【$.uid】提取到的结果是:56de2146104f480d8418d5400c978305
[2025-06-20 17:43:18,580] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:18,580] [INFO] [apitest] - 将提取的值【56de2146104f480d8418d5400c978305】保存为变量【uid】
[2025-06-20 17:43:18,580] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:18,582] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:18,583] [INFO] [apitest] - 通过【$.puid】提取到的结果是:c229ae481f104c4cbfda50264b8ccd02
[2025-06-20 17:43:18,583] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:18,584] [INFO] [apitest] - 将提取的值【c229ae481f104c4cbfda50264b8ccd02】保存为变量【puid】
[2025-06-20 17:43:18,584] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:18,585] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:18,586] [INFO] [apitest] - 通过【$.userId】提取到的结果是:7cff3c72501a4edabe4f344884af88a9
[2025-06-20 17:43:18,586] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:18,587] [INFO] [apitest] - 将提取的值【7cff3c72501a4edabe4f344884af88a9】保存为变量【userId】
[2025-06-20 17:43:18,587] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:18,588] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:18,588] [INFO] [apitest] - 请求地址：https://api-c.soboten.com/text/chat-kwb/conversation/summarySubmitVer2.action
[2025-06-20 17:43:18,589] [INFO] [apitest] - 请求参数 data：updateServiceId=xOh8P5f7pLDPHW5kyWJscuzHfKg8v3Wj5TWF2jP6WSj%2BIBS1zqXJr8fZ4ny5vE8t&uid=56de2146104f480d8418d5400c978305&cid=e54029bf5c9c452eb948b94cfb3fefe2&invalidSession=1
[2025-06-20 17:43:18,591] [DEBUG] [urllib3.connectionpool] - Starting new HTTPS connection (1): api-c.soboten.com:443
[2025-06-20 17:43:18,672] [DEBUG] [urllib3.connectionpool] - https://api-c.soboten.com:443 "POST /text/chat-kwb/conversation/summarySubmitVer2.action HTTP/1.1" 200 1370
[2025-06-20 17:43:18,673] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:18,673] [INFO] [apitest] - 响应体：{"summary":{"visitorId":"56de2146104f480d8418d5400c978305","cid":"e54029bf5c9c452eb948b94cfb3fefe2","companyId":"bdbe5660b2eb45758739527fda974091","platformId":"000","operationId":null,"operationName":null,"reqType":null,"reqTypeName":null,"questionStatus":-1,"questionDescription":null,"updateTime":1750412600768,"updateServiceId":"83d4039186d8452b88a59873bfcf2084","invalidSession":1,"partnerId":null,"summaryStaffName":"api","groupId":"","groupName":"","fieldId":null,"fieldName":null,"fieldValue":null,"createChatTime":1750412355266,"summaryCompanyId":"bdbe5660b2eb45758739527fda974091","permissionFlag":1,"summaryHandleProgress":null,"summaryTemplateId":null,"summaryClassifyNames":null,"summaryClassifyIds":null,"summaryRemark":null,"createAgentId":null,"createAgentName":null,"createAgentNo":null,"version":null,"customerFields":null,"updateAgentId":null,"updateAgentName":null,"updateAgentNo":null,"createTime":null,"summaryHandleProgressName":null,"summaryClassifyCodes":null,"summaryTemplateName":null,"summaryClassifyPaths":null,"summaryClassifyListLevel1":null,"summaryClassifyListLevel2":null,"summaryClassifyListLevel3":null,"summaryClassifyListLevel4":null,"summaryClassifyListLevel5":null,"summaryClassifyListLevel6":null,"aiClassificationState":null,"fullAiClassificationIds":null,"fullAiClassificationNames":null},"updateTime":1750412600768,"status":1}
[2025-06-20 17:43:18,674] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:18,688] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:18,689] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:18,689] [INFO] [apitest] - 请求地址：https://api-c.soboten.com/text/chat-kwb/admin/add_marklist.action
[2025-06-20 17:43:18,690] [INFO] [apitest] - 请求参数 data：sender=default_1750412594_10c95d07&receiver=e54029bf5c9c452eb948b94cfb3fefe2&uid=56de2146104f480d8418d5400c978305&cid=e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:18,691] [DEBUG] [urllib3.connectionpool] - Starting new HTTPS connection (1): api-c.soboten.com:443
[2025-06-20 17:43:18,798] [DEBUG] [urllib3.connectionpool] - https://api-c.soboten.com:443 "POST /text/chat-kwb/admin/add_marklist.action HTTP/1.1" 200 0
[2025-06-20 17:43:18,798] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:18,799] [INFO] [apitest] - 响应体：
[2025-06-20 17:43:18,799] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:18,813] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:18,813] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:18,814] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-20 17:43:18,814] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:18,815] [DEBUG] [urllib3.connectionpool] - Starting new HTTP connection (1): api-c.soboten.com:80
[2025-06-20 17:43:19,040] [DEBUG] [urllib3.connectionpool] - http://api-c.soboten.com:80 "POST /text/chat-visit/user/init/v6 HTTP/1.1" 200 None
[2025-06-20 17:43:19,074] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:19,074] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":"7cff3c72501a4edabe4f344884af88a9","userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"e0473f5fff564aa2887af80bcbddd56f:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":1,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":0,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"e54029bf5c9c452eb948b94cfb3fefe2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"c229ae481f104c4cbfda50264b8ccd02","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-20 17:43:19,076] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,076] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,078] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:19,078] [INFO] [apitest] - 通过【$.cid】提取到的结果是:e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:19,079] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,079] [INFO] [apitest] - 将提取的值【e54029bf5c9c452eb948b94cfb3fefe2】保存为变量【cid】
[2025-06-20 17:43:19,080] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,081] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:19,082] [INFO] [apitest] - 通过【$.uid】提取到的结果是:56de2146104f480d8418d5400c978305
[2025-06-20 17:43:19,082] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,083] [INFO] [apitest] - 将提取的值【56de2146104f480d8418d5400c978305】保存为变量【uid】
[2025-06-20 17:43:19,083] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,084] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:19,085] [INFO] [apitest] - 通过【$.puid】提取到的结果是:c229ae481f104c4cbfda50264b8ccd02
[2025-06-20 17:43:19,085] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,086] [INFO] [apitest] - 将提取的值【c229ae481f104c4cbfda50264b8ccd02】保存为变量【puid】
[2025-06-20 17:43:19,087] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,088] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:19,089] [INFO] [apitest] - 通过【$.userId】提取到的结果是:7cff3c72501a4edabe4f344884af88a9
[2025-06-20 17:43:19,089] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,090] [INFO] [apitest] - 将提取的值【7cff3c72501a4edabe4f344884af88a9】保存为变量【userId】
[2025-06-20 17:43:19,119] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,119] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:19,120] [INFO] [apitest] - 请求地址：https://api-c.soboten.com/text/chat-web/user/chatconnect.action
[2025-06-20 17:43:19,120] [INFO] [apitest] - 请求参数 data：sysNum=bdbe5660b2eb45758739527fda974091&chooseAdminId=&tranFlag=0&current=false&groupId=&transferType=0&summaryParams=&transferAction=&flowType=&flowCompanyId=&flowGroupId=&activeTransfer=1&unknownQuestion=&docId=&adminHelloWord=null&uid=56de2146104f480d8418d5400c978305&cid=e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:19,122] [DEBUG] [urllib3.connectionpool] - Starting new HTTPS connection (1): api-c.soboten.com:443
[2025-06-20 17:43:19,273] [DEBUG] [urllib3.connectionpool] - https://api-c.soboten.com:443 "POST /text/chat-web/user/chatconnect.action HTTP/1.1" 200 357
[2025-06-20 17:43:19,274] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:19,275] [INFO] [apitest] - 响应体：{"puid":"025da826775c489f8baa051e671c2d80","pu":"/webchat","queueFlag":null,"wslink.bak":["wss://imwsten.sobot.com:9001/ws","wss://imwsten.sobot.com:9002/ws","wss://imwsten.sobot.com:9003/ws","wss://imwsten.sobot.com:9004/ws","wss://imwsten.sobot.com:9005/ws","wss://imwsten.sobot.com:9006/ws"],"wslink.default":"wss://imwsten.sobot.com:9004/ws","status":2}
[2025-06-20 17:43:19,276] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,296] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,296] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:19,297] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-20 17:43:19,298] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:19,300] [DEBUG] [urllib3.connectionpool] - Starting new HTTP connection (1): api-c.soboten.com:80
[2025-06-20 17:43:19,522] [DEBUG] [urllib3.connectionpool] - http://api-c.soboten.com:80 "POST /text/chat-visit/user/init/v6 HTTP/1.1" 200 None
[2025-06-20 17:43:19,536] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:19,537] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":"7cff3c72501a4edabe4f344884af88a9","userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"df0c9d0d73554d7c85a5740aa6d8c5de:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":1,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":0,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"e54029bf5c9c452eb948b94cfb3fefe2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"c229ae481f104c4cbfda50264b8ccd02","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-20 17:43:19,545] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,547] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,554] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:19,560] [INFO] [apitest] - 通过【$.cid】提取到的结果是:e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:19,562] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,564] [INFO] [apitest] - 将提取的值【e54029bf5c9c452eb948b94cfb3fefe2】保存为变量【cid】
[2025-06-20 17:43:19,566] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,572] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:19,575] [INFO] [apitest] - 通过【$.uid】提取到的结果是:56de2146104f480d8418d5400c978305
[2025-06-20 17:43:19,576] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,577] [INFO] [apitest] - 将提取的值【56de2146104f480d8418d5400c978305】保存为变量【uid】
[2025-06-20 17:43:19,578] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,581] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:19,583] [INFO] [apitest] - 通过【$.puid】提取到的结果是:c229ae481f104c4cbfda50264b8ccd02
[2025-06-20 17:43:19,584] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,585] [INFO] [apitest] - 将提取的值【c229ae481f104c4cbfda50264b8ccd02】保存为变量【puid】
[2025-06-20 17:43:19,587] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,590] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:19,592] [INFO] [apitest] - 通过【$.userId】提取到的结果是:7cff3c72501a4edabe4f344884af88a9
[2025-06-20 17:43:19,592] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,593] [INFO] [apitest] - 将提取的值【7cff3c72501a4edabe4f344884af88a9】保存为变量【userId】
[2025-06-20 17:43:19,629] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,629] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:19,630] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-20 17:43:19,630] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:19,631] [DEBUG] [urllib3.connectionpool] - Starting new HTTP connection (1): api-c.soboten.com:80
[2025-06-20 17:43:19,888] [DEBUG] [urllib3.connectionpool] - http://api-c.soboten.com:80 "POST /text/chat-visit/user/init/v6 HTTP/1.1" 200 None
[2025-06-20 17:43:19,900] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:19,901] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":"7cff3c72501a4edabe4f344884af88a9","userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"adb7789685d34ff981d2074965dec81d:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":1,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":0,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"e54029bf5c9c452eb948b94cfb3fefe2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"c229ae481f104c4cbfda50264b8ccd02","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-20 17:43:19,904] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,905] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,910] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:19,911] [INFO] [apitest] - 通过【$.cid】提取到的结果是:e54029bf5c9c452eb948b94cfb3fefe2
[2025-06-20 17:43:19,912] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,913] [INFO] [apitest] - 将提取的值【e54029bf5c9c452eb948b94cfb3fefe2】保存为变量【cid】
[2025-06-20 17:43:19,914] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,919] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:19,921] [INFO] [apitest] - 通过【$.uid】提取到的结果是:56de2146104f480d8418d5400c978305
[2025-06-20 17:43:19,922] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,923] [INFO] [apitest] - 将提取的值【56de2146104f480d8418d5400c978305】保存为变量【uid】
[2025-06-20 17:43:19,924] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,926] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:19,928] [INFO] [apitest] - 通过【$.puid】提取到的结果是:c229ae481f104c4cbfda50264b8ccd02
[2025-06-20 17:43:19,929] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,930] [INFO] [apitest] - 将提取的值【c229ae481f104c4cbfda50264b8ccd02】保存为变量【puid】
[2025-06-20 17:43:19,931] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,938] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-20 17:43:19,939] [INFO] [apitest] - 通过【$.userId】提取到的结果是:7cff3c72501a4edabe4f344884af88a9
[2025-06-20 17:43:19,939] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:19,940] [INFO] [apitest] - 将提取的值【7cff3c72501a4edabe4f344884af88a9】保存为变量【userId】
[2025-06-20 17:43:19,941] [INFO] [apitest] - #######################################################################
[2025-06-20 17:43:19,941] [INFO] [apitest] - 请求方法：post
[2025-06-20 17:43:19,942] [INFO] [apitest] - 请求地址：https://api-c.soboten.com/text/chat-web/message/user/send.action
[2025-06-20 17:43:19,942] [INFO] [apitest] - 请求参数 data：uid=56de2146104f480d8418d5400c978305&cid=e54029bf5c9c452eb948b94cfb3fefe2&puid=c229ae481f104c4cbfda50264b8ccd02&content=%E4%BC%9A%E5%91%98%E6%B3%A8%E5%86%8C%E8%BF%98%E6%98%AF%E6%96%B9%E6%B3%95%E8%BF%99%E7%A7%8D%E7%9B%B8%E5%85%B3%E5%8A%A0%E5%85%A5.&objMsgType=&msgType=0&fileName=undefinded&msgId=56de2146104f480d8418d5400c9783051750412599941&resend=
[2025-06-20 17:43:19,944] [DEBUG] [urllib3.connectionpool] - Starting new HTTPS connection (1): api-c.soboten.com:443
[2025-06-20 17:43:20,018] [DEBUG] [urllib3.connectionpool] - https://api-c.soboten.com:443 "POST /text/chat-web/message/user/send.action HTTP/1.1" 200 62
[2025-06-20 17:43:20,018] [INFO] [apitest] - 响应码：200
[2025-06-20 17:43:20,019] [INFO] [apitest] - 响应体：{"retCode":"210029","retMsg":"用户已与客服断开连接"}
[2025-06-20 17:43:20,019] [INFO] [apitest] - #######################################################################


[2025-06-20 17:43:20,031] [INFO] [apitest] - 【测试完成】测试会话结束，清理环境
